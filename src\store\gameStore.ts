import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { 
  GameState, 
  GameLevel, 
  LevelProgress, 
  GameSettings, 
  Achievement, 
  GameStats 
} from '@/types'

interface GameStore extends GameState {
  // Level management
  currentLevel: GameLevel
  levelProgress: Record<GameLevel, LevelProgress>
  
  // Game settings
  settings: GameSettings
  
  // Statistics
  stats: GameStats
  
  // Actions
  setCurrentLevel: (level: GameLevel) => void
  completeLevel: (level: GameLevel, score?: number) => void
  resetGame: () => void
  updateSettings: (settings: Partial<GameSettings>) => void
  unlockAchievement: (achievementId: string) => void
  incrementPlayCount: () => void
  updatePlayTime: (timeSpent: number) => void
  
  // Quiz specific
  quizScore: number
  setQuizScore: (score: number) => void
  
  // Memory game specific
  memoryGameScore: number
  setMemoryGameScore: (score: number) => void
  
  // Photo gallery specific
  currentPhotoIndex: number
  setCurrentPhotoIndex: (index: number) => void
}

const defaultAchievements: Achievement[] = [
  {
    id: 'first-play',
    title: 'First Steps',
    description: 'Started your love journey!',
    emoji: '👶',
    isUnlocked: false,
  },
  {
    id: 'quiz-master',
    title: 'Quiz Master',
    description: 'Answered all quiz questions correctly!',
    emoji: '🧠',
    isUnlocked: false,
  },
  {
    id: 'memory-collector',
    title: 'Memory Collector',
    description: 'Captured all memories in the memory game!',
    emoji: '🎯',
    isUnlocked: false,
  },
  {
    id: 'photo-explorer',
    title: 'Photo Explorer',
    description: 'Viewed all photos in the gallery!',
    emoji: '📸',
    isUnlocked: false,
  },
  {
    id: 'love-champion',
    title: 'Love Champion',
    description: 'Completed the entire love journey!',
    emoji: '👑',
    isUnlocked: false,
  },
]

const initialLevelProgress: Record<GameLevel, LevelProgress> = {
  'welcome': { level: 'welcome', isComplete: false },
  'beginning-story': { level: 'beginning-story', isComplete: false },
  'memory-game': { level: 'memory-game', isComplete: false },
  'quiz-challenge': { level: 'quiz-challenge', isComplete: false },
  'photo-booth': { level: 'photo-booth', isComplete: false },
  'celebration': { level: 'celebration', isComplete: false },
}

export const useGameStore = create<GameStore>()(
  persist(
    (set, get) => ({
      // Initial state
      currentLevel: 'welcome',
      score: 0,
      isGameActive: false,
      completedLevels: [],
      levelProgress: initialLevelProgress,
      
      settings: {
        soundEnabled: true,
        animationsEnabled: true,
        language: 'en',
        difficulty: 'normal',
      },
      
      stats: {
        totalPlayTime: 0,
        levelsCompleted: 0,
        highScore: 0,
        achievements: defaultAchievements,
        playCount: 0,
      },
      
      quizScore: 0,
      memoryGameScore: 0,
      currentPhotoIndex: 0,
      
      // Actions
      setCurrentLevel: (level) => {
        set({ currentLevel: level })
        
        // Unlock first play achievement
        if (level !== 'welcome') {
          get().unlockAchievement('first-play')
        }
      },
      
      completeLevel: (level, score = 0) => {
        const state = get()
        const newProgress = {
          ...state.levelProgress,
          [level]: {
            ...state.levelProgress[level],
            isComplete: true,
            score,
            completedAt: new Date(),
          },
        }
        
        const completedCount = Object.values(newProgress).filter(p => p.isComplete).length
        const newHighScore = Math.max(state.stats.highScore, state.score + score)
        
        set({
          levelProgress: newProgress,
          score: state.score + score,
          stats: {
            ...state.stats,
            levelsCompleted: completedCount,
            highScore: newHighScore,
          },
        })
        
        // Check for achievements
        if (level === 'quiz-challenge' && score >= 3) {
          get().unlockAchievement('quiz-master')
        }
        if (level === 'memory-game' && score >= 10) {
          get().unlockAchievement('memory-collector')
        }
        if (level === 'photo-booth') {
          get().unlockAchievement('photo-explorer')
        }
        if (completedCount === 6) {
          get().unlockAchievement('love-champion')
        }
      },
      
      resetGame: () => {
        set({
          currentLevel: 'welcome',
          score: 0,
          isGameActive: false,
          completedLevels: [],
          levelProgress: initialLevelProgress,
          quizScore: 0,
          memoryGameScore: 0,
          currentPhotoIndex: 0,
        })
        get().incrementPlayCount()
      },
      
      updateSettings: (newSettings) => {
        set((state) => ({
          settings: { ...state.settings, ...newSettings },
        }))
      },
      
      unlockAchievement: (achievementId) => {
        set((state) => ({
          stats: {
            ...state.stats,
            achievements: state.stats.achievements.map((achievement) =>
              achievement.id === achievementId
                ? { ...achievement, isUnlocked: true, unlockedAt: new Date() }
                : achievement
            ),
          },
        }))
      },
      
      incrementPlayCount: () => {
        set((state) => ({
          stats: {
            ...state.stats,
            playCount: state.stats.playCount + 1,
            lastPlayedAt: new Date(),
          },
        }))
      },
      
      updatePlayTime: (timeSpent) => {
        set((state) => ({
          stats: {
            ...state.stats,
            totalPlayTime: state.stats.totalPlayTime + timeSpent,
          },
        }))
      },
      
      setQuizScore: (score) => set({ quizScore: score }),
      setMemoryGameScore: (score) => set({ memoryGameScore: score }),
      setCurrentPhotoIndex: (index) => set({ currentPhotoIndex: index }),
    }),
    {
      name: 'love-journey-game',
      partialize: (state) => ({
        levelProgress: state.levelProgress,
        settings: state.settings,
        stats: state.stats,
        score: state.score,
      }),
    }
  )
)
