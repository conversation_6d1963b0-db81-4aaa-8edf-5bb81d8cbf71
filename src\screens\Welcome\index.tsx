import React from 'react'
import { useNavigate } from 'react-router-dom'
import styled from '@emotion/styled'
import { motion } from 'framer-motion'
import { Card, Button, Typography } from '@/components/ui'
import { useGameStore } from '@/store/gameStore'

const WelcomeContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 120px);
  padding: ${({ theme }) => theme.spacing[4]};
  margin-top: 80px; /* Account for love meter */
`

const WelcomeCard = styled(Card)`
  max-width: 600px;
  width: 100%;
  text-align: center;
`

const CulturalElements = styled(motion.div)`
  font-size: 2.5rem;
  margin: ${({ theme }) => theme.spacing[6]} 0;
  display: flex;
  justify-content: center;
  gap: ${({ theme }) => theme.spacing[4]};
  flex-wrap: wrap;

  ${({ theme }) => theme.mediaQueries.mobile} {
    font-size: 2rem;
    gap: ${({ theme }) => theme.spacing[2]};
  }
`

const CulturalElement = styled(motion.span)`
  display: inline-block;
  cursor: pointer;
  transition: transform ${({ theme }) => theme.animations.durations.normal};

  &:hover {
    transform: scale(1.2) rotate(10deg);
  }
`

const WelcomeMessage = styled.div`
  margin: ${({ theme }) => theme.spacing[6]} 0;
  line-height: 1.8;

  .nepali-text {
    color: ${({ theme }) => theme.colors.lavender[600]};
    font-weight: ${({ theme }) => theme.typography.fontWeights.semibold};
  }

  .interactive-element {
    font-size: 2rem;
    margin: 0 ${({ theme }) => theme.spacing[2]};
    cursor: pointer;
    transition: transform ${({ theme }) => theme.animations.durations.normal};

    &:hover {
      transform: scale(1.3) rotate(15deg);
    }
  }
`

const culturalEmojis = ['🏔️', '🌸', '🦋', '✨', '🌺']

export const WelcomeScreen: React.FC = () => {
  const navigate = useNavigate()
  const { setCurrentLevel, resetGame } = useGameStore()

  const handleStartGame = () => {
    resetGame()
    setCurrentLevel('beginning-story')
    navigate('/beginning-story')
  }

  return (
    <WelcomeContainer>
      <WelcomeCard variant="romantic" padding="xl">
        <Typography 
          variant="h1" 
          gradient 
          animate
          style={{ marginBottom: '1rem' }}
        >
          💖 हाम्रो माया यात्रा 💖
        </Typography>
        
        <CulturalElements
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8, delay: 0.3 }}
        >
          {culturalEmojis.map((emoji, index) => (
            <CulturalElement
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ 
                duration: 0.5, 
                delay: 0.5 + index * 0.1,
                type: 'spring',
                stiffness: 100
              }}
              whileHover={{ 
                scale: 1.3, 
                rotate: 15,
                transition: { duration: 0.2 }
              }}
              whileTap={{ scale: 0.9 }}
            >
              {emoji}
            </CulturalElement>
          ))}
        </CulturalElements>

        <WelcomeMessage>
          <Typography 
            variant="body" 
            size="lg"
            animate
            style={{ marginBottom: '1.5rem' }}
          >
            <span className="nepali-text">मेरो प्रिय,</span> Welcome to our magical 1st Anniversary Adventure! 🎉
          </Typography>
          
          <Typography 
            variant="body"
            animate
            style={{ marginBottom: '1.5rem' }}
          >
            Just like the beautiful mountains of Nepal, our love reaches new heights every day!
          </Typography>
          
          <Typography 
            variant="body"
            animate
            style={{ marginBottom: '1.5rem' }}
          >
            Ready for a journey through our memories, मिठो moments, and surprises?
          </Typography>
          
          <motion.span 
            className="interactive-element"
            animate={{ 
              rotate: [0, 10, -10, 0],
              scale: [1, 1.1, 1]
            }}
            transition={{ 
              duration: 2, 
              repeat: Infinity,
              ease: 'easeInOut'
            }}
          >
            🕉️
          </motion.span>
        </WelcomeMessage>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 1 }}
        >
          <Button 
            variant="romantic" 
            size="lg"
            onClick={handleStartGame}
            aria-label="Start the love journey adventure"
          >
            <span className="nepali-text">सुरु गरौं</span> - Let's Begin Our Adventure! 💕
          </Button>
        </motion.div>
      </WelcomeCard>
    </WelcomeContainer>
  )
}

export default WelcomeScreen
