import React, { useState, useEffect, useCallback } from 'react'
import { useNavigate } from 'react-router-dom'
import styled from '@emotion/styled'
import { motion, AnimatePresence } from 'framer-motion'
import { Card, Button, Typography } from '@/components/ui'
import { useGameStore } from '@/store/gameStore'

const GameContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 120px);
  padding: ${({ theme }) => theme.spacing[4]};
  margin-top: 80px;
`

const GameCard = styled(Card)`
  max-width: 800px;
  width: 100%;
  text-align: center;
`

const GameArea = styled.div`
  position: relative;
  height: 400px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.3));
  border-radius: ${({ theme }) => theme.borderRadius['2xl']};
  margin: ${({ theme }) => theme.spacing[6]} 0;
  overflow: hidden;
  border: 2px solid rgba(255, 255, 255, 0.3);
`

const MemoryItem = styled(motion.div)<{ x: number; y: number }>`
  position: absolute;
  left: ${({ x }) => x}%;
  top: ${({ y }) => y}%;
  font-size: 2rem;
  cursor: pointer;
  user-select: none;
  z-index: 10;
  
  ${({ theme }) => theme.mediaQueries.mobile} {
    font-size: 1.5rem;
  }
`

const ScoreDisplay = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing[4]};
  
  ${({ theme }) => theme.mediaQueries.mobile} {
    flex-direction: column;
    gap: ${({ theme }) => theme.spacing[2]};
  }
`

const GameControls = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing[4]};
  justify-content: center;
  margin-top: ${({ theme }) => theme.spacing[6]};

  ${({ theme }) => theme.mediaQueries.mobile} {
    flex-direction: column;
    gap: ${({ theme }) => theme.spacing[3]};
  }
`

interface MemoryObject {
  id: string
  emoji: string
  text: string
  x: number
  y: number
  collected: boolean
}

const memoryItems = [
  { emoji: '💕', text: 'First "I love you"' },
  { emoji: '🌸', text: 'Spring walks together' },
  { emoji: '🎂', text: 'Birthday surprises' },
  { emoji: '🌙', text: 'Late night talks' },
  { emoji: '☕', text: 'Morning coffee dates' },
  { emoji: '🎵', text: 'Our favorite songs' },
  { emoji: '📸', text: 'Silly photo sessions' },
  { emoji: '🍕', text: 'Pizza night traditions' },
  { emoji: '🌟', text: 'Dream sharing' },
  { emoji: '🤗', text: 'Warm hugs' },
]

export const MemoryGameScreen: React.FC = () => {
  const [memories, setMemories] = useState<MemoryObject[]>([])
  const [score, setScore] = useState(0)
  const [timeLeft, setTimeLeft] = useState(60)
  const [gameActive, setGameActive] = useState(false)
  const [gameComplete, setGameComplete] = useState(false)
  
  const navigate = useNavigate()
  const { setCurrentLevel, completeLevel, setMemoryGameScore } = useGameStore()

  const generateMemories = useCallback(() => {
    const newMemories = memoryItems.slice(0, 8).map((item, index) => ({
      id: `memory-${index}`,
      emoji: item.emoji,
      text: item.text,
      x: Math.random() * 80 + 5, // 5-85% to keep within bounds
      y: Math.random() * 70 + 10, // 10-80% to keep within bounds
      collected: false,
    }))
    setMemories(newMemories)
  }, [])

  const startGame = () => {
    setGameActive(true)
    setScore(0)
    setTimeLeft(60)
    setGameComplete(false)
    generateMemories()
  }

  const collectMemory = (id: string) => {
    if (!gameActive) return
    
    setMemories(prev => 
      prev.map(memory => 
        memory.id === id ? { ...memory, collected: true } : memory
      )
    )
    setScore(prev => prev + 1)
  }

  const handleContinue = () => {
    completeLevel('memory-game', score)
    setMemoryGameScore(score)
    setCurrentLevel('quiz-challenge')
    navigate('/quiz-challenge')
  }

  // Timer effect
  useEffect(() => {
    if (!gameActive || timeLeft <= 0) return

    const timer = setInterval(() => {
      setTimeLeft(prev => {
        if (prev <= 1) {
          setGameActive(false)
          setGameComplete(true)
          return 0
        }
        return prev - 1
      })
    }, 1000)

    return () => clearInterval(timer)
  }, [gameActive, timeLeft])

  // Check if all memories collected
  useEffect(() => {
    if (gameActive && memories.length > 0 && memories.every(m => m.collected)) {
      setGameActive(false)
      setGameComplete(true)
    }
  }, [memories, gameActive])

  return (
    <GameContainer>
      <GameCard variant="glass" padding="xl">
        <Typography variant="h2" gradient>
          🧠 Memory Collection Game
        </Typography>
        
        <Typography variant="body" style={{ margin: '1rem 0' }}>
          <span style={{ color: '#9d6bff', fontWeight: 600 }}>सम्झनाहरू</span> - 
          Collect all our precious memories before time runs out!
        </Typography>

        <ScoreDisplay>
          <Typography variant="h4" color="#ec5a5a">
            Score: {score}/{memories.length}
          </Typography>
          <Typography variant="h4" color="#14b8a6">
            Time: {timeLeft}s
          </Typography>
        </ScoreDisplay>

        <GameArea>
          <AnimatePresence>
            {memories.map((memory) => (
              !memory.collected && (
                <MemoryItem
                  key={memory.id}
                  x={memory.x}
                  y={memory.y}
                  initial={{ scale: 0, opacity: 0 }}
                  animate={{ 
                    scale: 1, 
                    opacity: 1,
                    y: [0, -10, 0],
                  }}
                  exit={{ scale: 0, opacity: 0 }}
                  transition={{ 
                    duration: 0.5,
                    y: {
                      duration: 2,
                      repeat: Infinity,
                      ease: 'easeInOut'
                    }
                  }}
                  whileHover={{ scale: 1.2 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={() => collectMemory(memory.id)}
                  title={memory.text}
                >
                  {memory.emoji}
                </MemoryItem>
              )
            ))}
          </AnimatePresence>
        </GameArea>

        <GameControls>
          {!gameActive && !gameComplete && (
            <Button variant="romantic" size="lg" onClick={startGame}>
              Start Memory Hunt! 🎯
            </Button>
          )}
          
          {gameComplete && (
            <>
              <div style={{ textAlign: 'center', marginBottom: '1rem' }}>
                <Typography variant="h3" gradient>
                  {score === memories.length ? 'Perfect! 🎉' : 'Great job! 👏'}
                </Typography>
                <Typography variant="body">
                  You collected {score} out of {memories.length} memories!
                </Typography>
              </div>
              
              <Button variant="romantic" size="lg" onClick={handleContinue}>
                Continue Adventure! 🚀
              </Button>
              
              <Button variant="secondary" onClick={startGame}>
                Play Again 🔄
              </Button>
            </>
          )}
        </GameControls>
      </GameCard>
    </GameContainer>
  )
}

export default MemoryGameScreen
