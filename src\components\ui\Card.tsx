import React from 'react'
import styled from '@emotion/styled'
import { motion } from 'framer-motion'
import { Theme } from '@/theme'

interface CardProps {
  children: React.ReactNode
  variant?: 'default' | 'romantic' | 'dreamy' | 'glass'
  padding?: 'sm' | 'md' | 'lg' | 'xl'
  className?: string
  onClick?: () => void
  hoverable?: boolean
}

const StyledCard = styled(motion.div)<{
  variant: CardProps['variant']
  padding: CardProps['padding']
  hoverable?: boolean
  clickable?: boolean
}>`
  border-radius: ${({ theme }) => theme.borderRadius['3xl']};
  position: relative;
  overflow: hidden;
  transition: all ${({ theme }) => theme.animations.durations.normal} ${({ theme }) => theme.animations.easings.romantic};

  /* Padding variants */
  ${({ padding, theme }) => {
    switch (padding) {
      case 'sm':
        return `padding: ${theme.spacing[4]};`
      case 'lg':
        return `padding: ${theme.spacing[8]};`
      case 'xl':
        return `padding: ${theme.spacing[12]};`
      default:
        return `padding: ${theme.spacing[6]};`
    }
  }}

  /* Style variants */
  ${({ variant, theme }) => {
    switch (variant) {
      case 'romantic':
        return `
          background: linear-gradient(135deg, 
            rgba(236, 90, 90, 0.1) 0%, 
            rgba(157, 107, 255, 0.1) 50%, 
            rgba(249, 196, 60, 0.1) 100%
          );
          backdrop-filter: blur(20px);
          border: 2px solid rgba(236, 90, 90, 0.2);
          box-shadow: ${theme.shadows.romantic};
        `
      case 'dreamy':
        return `
          background: linear-gradient(135deg, 
            rgba(157, 107, 255, 0.1) 0%, 
            rgba(20, 184, 166, 0.1) 100%
          );
          backdrop-filter: blur(20px);
          border: 2px solid rgba(157, 107, 255, 0.2);
          box-shadow: ${theme.shadows.dreamy};
        `
      case 'glass':
        return `
          background: rgba(255, 255, 255, 0.95);
          backdrop-filter: blur(20px);
          border: 2px solid rgba(255, 255, 255, 0.3);
          box-shadow: ${theme.shadows.xl};
        `
      default:
        return `
          background: rgba(255, 255, 255, 0.98);
          border: 2px solid rgba(255, 255, 255, 0.5);
          box-shadow: ${theme.shadows.lg};
        `
    }
  }}

  /* Hoverable styles */
  ${({ hoverable, clickable, theme }) =>
    (hoverable || clickable) &&
    `
    cursor: ${clickable ? 'pointer' : 'default'};
    
    &:hover {
      transform: translateY(-4px) scale(1.01);
      box-shadow: ${theme.shadows['2xl']};
    }
  `}

  /* Rotating border effect for romantic variant */
  ${({ variant }) =>
    variant === 'romantic' &&
    `
    &::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: conic-gradient(
        from 0deg,
        transparent,
        rgba(236, 90, 90, 0.3),
        transparent
      );
      animation: rotate 20s linear infinite;
      z-index: -1;
    }

    @keyframes rotate {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }
  `}
`

const CardContent = styled.div`
  position: relative;
  z-index: 1;
`

export const Card: React.FC<CardProps> = ({
  children,
  variant = 'default',
  padding = 'md',
  className,
  onClick,
  hoverable = false,
  ...props
}) => {
  const isClickable = Boolean(onClick)

  return (
    <StyledCard
      variant={variant}
      padding={padding}
      hoverable={hoverable}
      clickable={isClickable}
      className={className}
      onClick={onClick}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{
        duration: 0.5,
        ease: 'easeOut',
      }}
      whileTap={isClickable ? { scale: 0.98 } : undefined}
      {...props}
    >
      <CardContent>{children}</CardContent>
    </StyledCard>
  )
}

export default Card
