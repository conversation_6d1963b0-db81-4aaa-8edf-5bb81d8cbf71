export type {
  <PERSON>S<PERSON>,
  Quiz<PERSON><PERSON><PERSON>,
  <PERSON><PERSON>O<PERSON>,
  QuizState,
  MemoryGameState,
  Memory,
  Particle,
  PhotoMemory,
  PhotoGalleryState,
  GameLevel,
  LevelProgress,
  GameSettings,
  Achievement,
  GameStats,
} from './game'

// Common utility types
export interface ApiResponse<T> {
  data: T
  success: boolean
  message?: string
  error?: string
}

export interface LoadingState {
  isLoading: boolean
  error?: string
}

// Animation types
export interface AnimationConfig {
  duration?: number
  delay?: number
  ease?: string
  repeat?: number
}

// Event types
export interface GameEvent {
  type: string
  payload?: any
  timestamp: number
}

// Component prop types
export interface BaseComponentProps {
  className?: string
  children?: React.ReactNode
  'data-testid'?: string
}

// Form types
export interface FormField {
  name: string
  value: any
  error?: string
  touched?: boolean
}

// Navigation types
export interface NavigationItem {
  path: string
  label: string
  icon?: string
  isActive?: boolean
}
