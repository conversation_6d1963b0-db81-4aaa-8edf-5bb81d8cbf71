export interface GameState {
  currentLevel: number
  score: number
  isGameActive: boolean
  completedLevels: number[]
  playerName?: string
}

export interface QuizQuestion {
  id: number
  question: string
  options: QuizOption[]
  correctAnswer: string
  explanation?: string
}

export interface QuizOption {
  id: string
  text: string
  emoji?: string
}

export interface QuizState {
  currentQuestion: number
  score: number
  answers: Record<number, string>
  isComplete: boolean
}

export interface MemoryGameState {
  score: number
  isActive: boolean
  memories: Memory[]
  particles: Particle[]
  timeRemaining?: number
}

export interface Memory {
  id: string
  x: number
  y: number
  vx: number
  vy: number
  emoji: string
  text: string
  life: number
  scale: number
  rotation: number
}

export interface Particle {
  id: string
  x: number
  y: number
  vx: number
  vy: number
  life: number
  color: string
}

export interface PhotoMemory {
  id: string
  src: string
  title: string
  subtitle: string
  description?: string
  date?: string
}

export interface PhotoGalleryState {
  currentPhotoIndex: number
  photos: PhotoMemory[]
  isLoading: boolean
  error?: string
}

export type GameLevel = 
  | 'welcome'
  | 'beginning-story'
  | 'memory-game'
  | 'quiz-challenge'
  | 'photo-booth'
  | 'celebration'

export interface LevelProgress {
  level: GameLevel
  isComplete: boolean
  score?: number
  timeSpent?: number
  completedAt?: Date
}

export interface GameSettings {
  soundEnabled: boolean
  animationsEnabled: boolean
  language: 'en' | 'ne'
  difficulty: 'easy' | 'normal' | 'hard'
}

export interface Achievement {
  id: string
  title: string
  description: string
  emoji: string
  unlockedAt?: Date
  isUnlocked: boolean
}

export interface GameStats {
  totalPlayTime: number
  levelsCompleted: number
  highScore: number
  achievements: Achievement[]
  playCount: number
  lastPlayedAt?: Date
}
