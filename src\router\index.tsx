import React from 'react'
import { createBrowserRouter, Navigate } from 'react-router-dom'
import { GameLayout } from '@/components/layout/GameLayout'
import { ErrorBoundary } from '@/components/ErrorBoundary'

// Lazy load screens for better performance
const WelcomeScreen = React.lazy(() => import('@/screens/Welcome'))
const BeginningStoryScreen = React.lazy(() => import('@/screens/BeginningStory'))
const MemoryGameScreen = React.lazy(() => import('@/screens/MemoryGame'))
const QuizChallengeScreen = React.lazy(() => import('@/screens/QuizChallenge'))
const PhotoBoothScreen = React.lazy(() => import('@/screens/PhotoBooth'))
const CelebrationScreen = React.lazy(() => import('@/screens/Celebration'))

// Loading component for lazy-loaded routes
const LoadingScreen = () => (
  <div style={{ 
    display: 'flex', 
    justifyContent: 'center', 
    alignItems: 'center', 
    height: '100vh',
    fontSize: '1.5rem',
    color: '#ec5a5a'
  }}>
    Loading... 💕
  </div>
)

// Wrapper component for lazy loading with suspense
const LazyWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ErrorBoundary>
    <React.Suspense fallback={<LoadingScreen />}>
      {children}
    </React.Suspense>
  </ErrorBoundary>
)

export const router = createBrowserRouter([
  {
    path: '/',
    element: <GameLayout />,
    errorElement: <ErrorBoundary />,
    children: [
      {
        index: true,
        element: <Navigate to="/welcome" replace />,
      },
      {
        path: 'welcome',
        element: (
          <LazyWrapper>
            <WelcomeScreen />
          </LazyWrapper>
        ),
      },
      {
        path: 'beginning-story',
        element: (
          <LazyWrapper>
            <BeginningStoryScreen />
          </LazyWrapper>
        ),
      },
      {
        path: 'memory-game',
        element: (
          <LazyWrapper>
            <MemoryGameScreen />
          </LazyWrapper>
        ),
      },
      {
        path: 'quiz-challenge',
        element: (
          <LazyWrapper>
            <QuizChallengeScreen />
          </LazyWrapper>
        ),
      },
      {
        path: 'photo-booth',
        element: (
          <LazyWrapper>
            <PhotoBoothScreen />
          </LazyWrapper>
        ),
      },
      {
        path: 'celebration',
        element: (
          <LazyWrapper>
            <CelebrationScreen />
          </LazyWrapper>
        ),
      },
    ],
  },
  {
    path: '*',
    element: <Navigate to="/welcome" replace />,
  },
])

export default router
