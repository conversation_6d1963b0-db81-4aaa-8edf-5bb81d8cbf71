import React, { useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import styled from '@emotion/styled'
import { motion, AnimatePresence } from 'framer-motion'
import { Card, Button, Typography } from '@/components/ui'
import { useGameStore } from '@/store/gameStore'

const CelebrationContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 120px);
  padding: ${({ theme }) => theme.spacing[4]};
  margin-top: 80px;
  position: relative;
`

const CelebrationCard = styled(Card)`
  max-width: 800px;
  width: 100%;
  text-align: center;
  position: relative;
  z-index: 10;
`

const Confetti = styled(motion.div)<{ x: number; color: string }>`
  position: absolute;
  left: ${({ x }) => x}%;
  font-size: 2rem;
  z-index: 1;
  pointer-events: none;
`

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: ${({ theme }) => theme.spacing[4]};
  margin: ${({ theme }) => theme.spacing[6]} 0;
`

const StatCard = styled(motion.div)`
  background: rgba(255, 255, 255, 0.2);
  padding: ${({ theme }) => theme.spacing[4]};
  border-radius: ${({ theme }) => theme.borderRadius['2xl']};
  border: 2px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
`

const AchievementsList = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: ${({ theme }) => theme.spacing[2]};
  justify-content: center;
  margin: ${({ theme }) => theme.spacing[4]} 0;
`

const AchievementBadge = styled(motion.div)<{ unlocked: boolean }>`
  padding: ${({ theme }) => theme.spacing[2]} ${({ theme }) => theme.spacing[3]};
  background: ${({ unlocked, theme }) => 
    unlocked ? theme.colors.gradients.romantic : 'rgba(255, 255, 255, 0.1)'};
  border-radius: ${({ theme }) => theme.borderRadius.full};
  border: 2px solid ${({ unlocked, theme }) => 
    unlocked ? theme.colors.blushPink[300] : theme.colors.neutral[300]};
  opacity: ${({ unlocked }) => unlocked ? 1 : 0.5};
  color: ${({ unlocked }) => unlocked ? 'white' : 'inherit'};
  font-size: ${({ theme }) => theme.typography.fontSizes.sm};
  font-weight: ${({ theme }) => theme.typography.fontWeights.semibold};
`

const ActionButtons = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing[4]};
  justify-content: center;
  margin-top: ${({ theme }) => theme.spacing[8]};

  ${({ theme }) => theme.mediaQueries.mobile} {
    flex-direction: column;
    gap: ${({ theme }) => theme.spacing[3]};
  }
`

const confettiEmojis = ['🎉', '🎊', '💖', '✨', '🌟', '💕', '🦋', '🌸']

export const CelebrationScreen: React.FC = () => {
  const [confettiPieces, setConfettiPieces] = useState<Array<{
    id: string
    emoji: string
    x: number
    color: string
  }>>([])
  
  const navigate = useNavigate()
  const { 
    stats, 
    score, 
    quizScore, 
    memoryGameScore, 
    levelProgress,
    resetGame,
    completeLevel 
  } = useGameStore()

  // Generate confetti effect
  useEffect(() => {
    completeLevel('celebration', 10)
    
    const generateConfetti = () => {
      const pieces = Array.from({ length: 20 }, (_, i) => ({
        id: `confetti-${i}`,
        emoji: confettiEmojis[Math.floor(Math.random() * confettiEmojis.length)],
        x: Math.random() * 100,
        color: `hsl(${Math.random() * 360}, 70%, 60%)`
      }))
      setConfettiPieces(pieces)
    }

    generateConfetti()
    const interval = setInterval(generateConfetti, 3000)
    
    return () => clearInterval(interval)
  }, [completeLevel])

  const handlePlayAgain = () => {
    resetGame()
    navigate('/welcome')
  }

  const totalScore = score + quizScore + memoryGameScore
  const completedLevels = Object.values(levelProgress).filter(p => p.isComplete).length
  const unlockedAchievements = stats.achievements.filter(a => a.isUnlocked)

  return (
    <CelebrationContainer>
      {/* Confetti Animation */}
      <AnimatePresence>
        {confettiPieces.map((piece) => (
          <Confetti
            key={piece.id}
            x={piece.x}
            color={piece.color}
            initial={{ y: -100, opacity: 1, rotate: 0 }}
            animate={{ 
              y: window.innerHeight + 100, 
              opacity: 0,
              rotate: 360
            }}
            exit={{ opacity: 0 }}
            transition={{ 
              duration: 3,
              ease: 'easeOut'
            }}
          >
            {piece.emoji}
          </Confetti>
        ))}
      </AnimatePresence>

      <CelebrationCard variant="romantic" padding="xl">
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.8, type: 'spring' }}
        >
          <Typography variant="h1" gradient style={{ marginBottom: '1rem' }}>
            🎊 Happy 1st Anniversary! 🎊
          </Typography>
          
          <Typography variant="h3" nepali style={{ marginBottom: '2rem' }}>
            हाम्रो पहिलो वर्षगाँठको शुभकामना! 💕
          </Typography>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5, duration: 0.6 }}
        >
          <Typography variant="body" size="lg" style={{ marginBottom: '2rem' }}>
            You've completed our love journey adventure! Here's to many more years of 
            laughter, love, and beautiful memories together. 
            <br />
            <span style={{ color: '#9d6bff', fontWeight: 600 }}>
              तिमी मेरो जीवनको सबैभन्दा राम्रो उपहार हौ! 💖
            </span>
          </Typography>
        </motion.div>

        <StatsGrid>
          <StatCard
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.7, duration: 0.5 }}
          >
            <Typography variant="h3" gradient>{totalScore}</Typography>
            <Typography variant="caption">Total Love Points</Typography>
          </StatCard>
          
          <StatCard
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.8, duration: 0.5 }}
          >
            <Typography variant="h3" gradient>{completedLevels}/6</Typography>
            <Typography variant="caption">Levels Completed</Typography>
          </StatCard>
          
          <StatCard
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.9, duration: 0.5 }}
          >
            <Typography variant="h3" gradient>{unlockedAchievements.length}</Typography>
            <Typography variant="caption">Achievements</Typography>
          </StatCard>
        </StatsGrid>

        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1, duration: 0.5 }}
        >
          <Typography variant="h4" style={{ marginBottom: '1rem' }}>
            🏆 Achievements Unlocked
          </Typography>
          
          <AchievementsList>
            {stats.achievements.map((achievement, index) => (
              <AchievementBadge
                key={achievement.id}
                unlocked={achievement.isUnlocked}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 1.2 + index * 0.1, duration: 0.3 }}
                whileHover={{ scale: 1.05 }}
                title={achievement.description}
              >
                {achievement.emoji} {achievement.title}
              </AchievementBadge>
            ))}
          </AchievementsList>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.5, duration: 0.5 }}
        >
          <Typography variant="body" style={{ 
            margin: '2rem 0',
            fontStyle: 'italic',
            color: '#666'
          }}>
            "Every love story is beautiful, but ours is my favorite." 💕
            <br />
            <span style={{ color: '#9d6bff' }}>
              "हरेक माया कथा सुन्दर हुन्छ, तर हाम्रो त मेरो मनपर्ने हो।"
            </span>
          </Typography>
        </motion.div>

        <ActionButtons>
          <Button 
            variant="romantic" 
            size="lg"
            onClick={handlePlayAgain}
          >
            Play Again 🔄
          </Button>
          
          <Button 
            variant="dreamy" 
            size="lg"
            onClick={() => window.location.reload()}
          >
            Share Our Love 💌
          </Button>
        </ActionButtons>
      </CelebrationCard>
    </CelebrationContainer>
  )
}

export default CelebrationScreen
