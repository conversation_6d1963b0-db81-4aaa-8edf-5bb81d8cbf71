import React from 'react'
import styled from '@emotion/styled'
import { motion } from 'framer-motion'
import { Theme } from '@/theme'

interface ButtonProps {
  children: React.ReactNode
  variant?: 'primary' | 'secondary' | 'romantic' | 'dreamy'
  size?: 'sm' | 'md' | 'lg'
  disabled?: boolean
  loading?: boolean
  onClick?: () => void
  type?: 'button' | 'submit' | 'reset'
  className?: string
  'aria-label'?: string
}

const StyledButton = styled(motion.button)<{
  variant: ButtonProps['variant']
  size: ButtonProps['size']
  disabled?: boolean
}>`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-family: ${({ theme }) => theme.typography.fonts.primary};
  font-weight: ${({ theme }) => theme.typography.fontWeights.semibold};
  border-radius: ${({ theme }) => theme.borderRadius['2xl']};
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all ${({ theme }) => theme.animations.durations.normal} ${({ theme }) => theme.animations.easings.romantic};

  /* Size variants */
  ${({ size, theme }) => {
    switch (size) {
      case 'sm':
        return `
          padding: ${theme.spacing[2]} ${theme.spacing[4]};
          font-size: ${theme.typography.fontSizes.sm};
          min-height: 36px;
        `
      case 'lg':
        return `
          padding: ${theme.spacing[4]} ${theme.spacing[8]};
          font-size: ${theme.typography.fontSizes.xl};
          min-height: 56px;
        `
      default:
        return `
          padding: ${theme.spacing[3]} ${theme.spacing[6]};
          font-size: ${theme.typography.fontSizes.base};
          min-height: 44px;
        `
    }
  }}

  /* Color variants */
  ${({ variant, theme }) => {
    switch (variant) {
      case 'secondary':
        return `
          background: ${theme.colors.neutral[100]};
          color: ${theme.colors.neutral[700]};
          border: 2px solid ${theme.colors.neutral[200]};
          
          &:hover:not(:disabled) {
            background: ${theme.colors.neutral[200]};
            border-color: ${theme.colors.neutral[300]};
            transform: translateY(-2px);
            box-shadow: ${theme.shadows.md};
          }
        `
      case 'romantic':
        return `
          background: ${theme.colors.gradients.romantic};
          color: white;
          border: none;
          box-shadow: ${theme.shadows.romantic};
          
          &:hover:not(:disabled) {
            transform: translateY(-3px) scale(1.02);
            box-shadow: ${theme.shadows.romantic}, ${theme.shadows.lg};
          }
        `
      case 'dreamy':
        return `
          background: ${theme.colors.gradients.sunset};
          color: white;
          border: none;
          box-shadow: ${theme.shadows.dreamy};
          
          &:hover:not(:disabled) {
            transform: translateY(-3px) scale(1.02);
            box-shadow: ${theme.shadows.dreamy}, ${theme.shadows.lg};
          }
        `
      default:
        return `
          background: linear-gradient(45deg, ${theme.colors.blushPink[500]}, ${theme.colors.lavender[500]});
          color: white;
          border: none;
          box-shadow: ${theme.shadows.lg};
          
          &:hover:not(:disabled) {
            transform: translateY(-3px) scale(1.02);
            box-shadow: ${theme.shadows.xl};
          }
        `
    }
  }}

  /* Disabled state */
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
  }

  /* Active state */
  &:active:not(:disabled) {
    transform: translateY(-1px) scale(1.01);
  }

  /* Shimmer effect */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left ${({ theme }) => theme.animations.durations.slow};
  }

  &:hover:not(:disabled)::before {
    left: 100%;
  }

  /* Focus styles for accessibility */
  &:focus-visible {
    outline: 2px solid ${({ theme }) => theme.colors.blushPink[500]};
    outline-offset: 2px;
  }
`

const LoadingSpinner = styled(motion.div)`
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  margin-right: ${({ theme }) => theme.spacing[2]};
`

export const Button: React.FC<ButtonProps> = ({
  children,
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  onClick,
  type = 'button',
  className,
  'aria-label': ariaLabel,
  ...props
}) => {
  return (
    <StyledButton
      variant={variant}
      size={size}
      disabled={disabled || loading}
      onClick={onClick}
      type={type}
      className={className}
      aria-label={ariaLabel}
      whileTap={{ scale: disabled ? 1 : 0.98 }}
      {...props}
    >
      {loading && (
        <LoadingSpinner
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
        />
      )}
      {children}
    </StyledButton>
  )
}

export default Button
