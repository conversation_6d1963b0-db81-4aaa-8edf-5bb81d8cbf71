import React from 'react'
import { Outlet } from 'react-router-dom'
import styled from '@emotion/styled'
import { motion } from 'framer-motion'
import { FloatingHearts } from '@/components/animations/FloatingHearts'
import { BackgroundEffects } from '@/components/animations/BackgroundEffects'

const LayoutContainer = styled.div`
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
  background: ${({ theme }) => theme.colors.gradients.dreamy};
  background-size: 600% 600%;
  animation: gradientShift 12s ease infinite;

  @keyframes gradientShift {
    0% { background-position: 0% 50%; }
    25% { background-position: 50% 0%; }
    50% { background-position: 100% 50%; }
    75% { background-position: 50% 100%; }
    100% { background-position: 0% 50%; }
  }
`

const MainContent = styled(motion.main)`
  max-width: 900px;
  margin: 0 auto;
  padding: ${({ theme }) => theme.spacing[5]};
  position: relative;
  z-index: 10;

  ${({ theme }) => theme.mediaQueries.mobile} {
    padding: ${({ theme }) => theme.spacing[4]};
  }
`

/* const LoveMeterContainer = styled.div`
  position: fixed;
  top: ${({ theme }) => theme.spacing[4]};
  left: 50%;
  transform: translateX(-50%);
  z-index: 100;
  width: 90%;
  max-width: 400px;

  ${({ theme }) => theme.mediaQueries.mobile} {
    top: ${({ theme }) => theme.spacing[2]};
    width: 95%;
  }
` */

export const GameLayout: React.FC = () => {
  return (
    <LayoutContainer>
      {/* Background effects */}
      <BackgroundEffects />
      
      {/* Floating hearts animation */}
      <FloatingHearts />
      
      {/* Love meter at the top */}
      {/* <LoveMeterContainer>
        <LoveMeter />
      </LoveMeterContainer> */}
      
      {/* Main content area */}
      <MainContent
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        <Outlet />
      </MainContent>
    </LayoutContainer>
  )
}

export default GameLayout
