import React from 'react'
import { RouterProvider } from 'react-router-dom'
import { ThemeProvider } from '@/theme/ThemeProvider'
import { router } from '@/router'
import { ErrorBoundary } from '@/components/ErrorBoundary'

function App() {
  return (
    <ErrorBoundary>
      <ThemeProvider>
        <RouterProvider router={router} />
      </ThemeProvider>
    </ErrorBoundary>
  )
}

export default App
