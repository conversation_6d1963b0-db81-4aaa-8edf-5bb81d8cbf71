import React from 'react'
import styled from '@emotion/styled'
import { motion } from 'framer-motion'
import { Theme, FontSize, FontWeight } from '@/theme'

interface TypographyProps {
  children: React.ReactNode
  variant?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'body' | 'caption' | 'overline'
  size?: FontSize
  weight?: FontWeight
  color?: string
  align?: 'left' | 'center' | 'right' | 'justify'
  gradient?: boolean
  nepali?: boolean
  className?: string
  as?: keyof JSX.IntrinsicElements
  animate?: boolean
}

const StyledText = styled(motion.div)<{
  variant: TypographyProps['variant']
  size?: FontSize
  weight?: FontWeight
  color?: string
  align?: TypographyProps['align']
  gradient?: boolean
  nepali?: boolean
}>`
  font-family: ${({ theme }) => theme.typography.fonts.primary};
  margin: 0;
  
  /* Variant styles */
  ${({ variant, theme }) => {
    switch (variant) {
      case 'h1':
        return `
          font-size: ${theme.typography.fontSizes['5xl']};
          font-weight: ${theme.typography.fontWeights.bold};
          line-height: ${theme.typography.lineHeights.tight};
          margin-bottom: ${theme.spacing[6]};
          
          @media (max-width: ${theme.breakpoints.md}) {
            font-size: ${theme.typography.fontSizes['4xl']};
          }
        `
      case 'h2':
        return `
          font-size: ${theme.typography.fontSizes['4xl']};
          font-weight: ${theme.typography.fontWeights.semibold};
          line-height: ${theme.typography.lineHeights.tight};
          margin-bottom: ${theme.spacing[4]};
          
          @media (max-width: ${theme.breakpoints.md}) {
            font-size: ${theme.typography.fontSizes['3xl']};
          }
        `
      case 'h3':
        return `
          font-size: ${theme.typography.fontSizes['3xl']};
          font-weight: ${theme.typography.fontWeights.semibold};
          line-height: ${theme.typography.lineHeights.snug};
          margin-bottom: ${theme.spacing[3]};
          
          @media (max-width: ${theme.breakpoints.md}) {
            font-size: ${theme.typography.fontSizes['2xl']};
          }
        `
      case 'h4':
        return `
          font-size: ${theme.typography.fontSizes['2xl']};
          font-weight: ${theme.typography.fontWeights.medium};
          line-height: ${theme.typography.lineHeights.snug};
          margin-bottom: ${theme.spacing[3]};
        `
      case 'h5':
        return `
          font-size: ${theme.typography.fontSizes.xl};
          font-weight: ${theme.typography.fontWeights.medium};
          line-height: ${theme.typography.lineHeights.snug};
          margin-bottom: ${theme.spacing[2]};
        `
      case 'h6':
        return `
          font-size: ${theme.typography.fontSizes.lg};
          font-weight: ${theme.typography.fontWeights.medium};
          line-height: ${theme.typography.lineHeights.snug};
          margin-bottom: ${theme.spacing[2]};
        `
      case 'caption':
        return `
          font-size: ${theme.typography.fontSizes.sm};
          font-weight: ${theme.typography.fontWeights.normal};
          line-height: ${theme.typography.lineHeights.normal};
          color: ${theme.colors.neutral[600]};
        `
      case 'overline':
        return `
          font-size: ${theme.typography.fontSizes.xs};
          font-weight: ${theme.typography.fontWeights.semibold};
          line-height: ${theme.typography.lineHeights.normal};
          text-transform: uppercase;
          letter-spacing: ${theme.typography.letterSpacing.wider};
          color: ${theme.colors.neutral[600]};
        `
      default:
        return `
          font-size: ${theme.typography.fontSizes.base};
          font-weight: ${theme.typography.fontWeights.normal};
          line-height: ${theme.typography.lineHeights.relaxed};
        `
    }
  }}

  /* Custom size override */
  ${({ size, theme }) => size && `font-size: ${theme.typography.fontSizes[size]};`}

  /* Custom weight override */
  ${({ weight, theme }) => weight && `font-weight: ${theme.typography.fontWeights[weight]};`}

  /* Custom color */
  ${({ color }) => color && `color: ${color};`}

  /* Text alignment */
  ${({ align }) => align && `text-align: ${align};`}

  /* Gradient text effect */
  ${({ gradient, theme }) =>
    gradient &&
    `
    background: ${theme.colors.gradients.romantic};
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: none;
  `}

  /* Nepali text styling */
  ${({ nepali, theme }) =>
    nepali &&
    `
    color: ${theme.colors.lavender[600]};
    font-weight: ${theme.typography.fontWeights.semibold};
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
  `}
`

const getSemanticTag = (variant: TypographyProps['variant']) => {
  switch (variant) {
    case 'h1':
    case 'h2':
    case 'h3':
    case 'h4':
    case 'h5':
    case 'h6':
      return variant
    case 'caption':
      return 'small'
    case 'overline':
      return 'span'
    default:
      return 'p'
  }
}

export const Typography: React.FC<TypographyProps> = ({
  children,
  variant = 'body',
  size,
  weight,
  color,
  align,
  gradient = false,
  nepali = false,
  className,
  as,
  animate = false,
  ...props
}) => {
  const component = as || getSemanticTag(variant)

  const animationProps = animate
    ? {
        initial: { opacity: 0, y: 20 },
        animate: { opacity: 1, y: 0 },
        transition: { duration: 0.5, ease: 'easeOut' },
      }
    : {}

  return (
    <StyledText
      as={component}
      variant={variant}
      size={size}
      weight={weight}
      color={color}
      align={align}
      gradient={gradient}
      nepali={nepali}
      className={className}
      {...animationProps}
      {...props}
    >
      {children}
    </StyledText>
  )
}

export default Typography
