import { colors } from './colors'
import { typography } from './typography'
import { spacing, borderRadius, shadows } from './spacing'
import { breakpoints, mediaQueries } from './breakpoints'
import { animations } from './animations'

export const theme = {
  colors,
  typography,
  spacing,
  borderRadius,
  shadows,
  breakpoints,
  mediaQueries,
  animations,
} as const

export type Theme = typeof theme

// Export individual theme parts for convenience
export { colors, typography, spacing, borderRadius, shadows, breakpoints, mediaQueries, animations }

// Export types
export type {
  ColorKey,
  ColorShade,
  FontSize,
  FontWeight,
  SpacingKey,
  BorderRadiusKey,
  ShadowKey,
  BreakpointKey,
  MediaQueryKey,
  AnimationDuration,
  AnimationEasing,
  AnimationKeyframe,
} from './colors'
