import React from 'react'
import styled from '@emotion/styled'
import { motion } from 'framer-motion'
import { useGameStore } from '@/store/gameStore'

const EffectsContainer = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
  overflow: hidden;
`

const PatternOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='20' font-size='20' fill='rgba(255,255,255,0.05)'>🌸</text><text x='30' y='40' font-size='15' fill='rgba(255,255,255,0.05)'>🦋</text><text x='60' y='30' font-size='18' fill='rgba(255,255,255,0.05)'>✨</text><text x='20' y='70' font-size='16' fill='rgba(255,255,255,0.05)'>🌺</text><text x='80' y='80' font-size='14' fill='rgba(255,255,255,0.05)'>💫</text></svg>");
  background-repeat: repeat;
  opacity: 0.3;
`

const FloatingElement = styled(motion.div)<{ 
  size: number
  color: string
  x: number
  y: number
}>`
  position: absolute;
  width: ${({ size }) => size}px;
  height: ${({ size }) => size}px;
  background: ${({ color }) => color};
  border-radius: 50%;
  left: ${({ x }) => x}%;
  top: ${({ y }) => y}%;
  filter: blur(1px);
  opacity: 0.6;
`

const MusicNote = styled(motion.div)<{ x: number; y: number }>`
  position: absolute;
  left: ${({ x }) => x}%;
  top: ${({ y }) => y}%;
  font-size: 30px;
  opacity: 0.7;
  user-select: none;
`

const musicNotes = ['🎵', '🎶', '💃', '🎭']

const floatingElements = Array.from({ length: 8 }, (_, i) => ({
  id: i,
  size: Math.random() * 20 + 10,
  color: `rgba(${Math.random() * 255}, ${Math.random() * 255}, ${Math.random() * 255}, 0.1)`,
  x: Math.random() * 100,
  y: Math.random() * 100,
  duration: Math.random() * 10 + 15,
}))

const musicElements = Array.from({ length: 4 }, (_, i) => ({
  id: i,
  emoji: musicNotes[i],
  x: Math.random() * 100,
  y: Math.random() * 100,
  duration: Math.random() * 6 + 8,
}))

export const BackgroundEffects: React.FC = () => {
  const { settings } = useGameStore()

  if (!settings.animationsEnabled) {
    return (
      <EffectsContainer>
        <PatternOverlay />
      </EffectsContainer>
    )
  }

  return (
    <EffectsContainer>
      <PatternOverlay />
      
      {/* Floating geometric elements */}
      {floatingElements.map((element) => (
        <FloatingElement
          key={element.id}
          size={element.size}
          color={element.color}
          x={element.x}
          y={element.y}
          animate={{
            y: [element.y, element.y - 20, element.y],
            x: [element.x, element.x + 10, element.x],
            scale: [1, 1.2, 1],
          }}
          transition={{
            duration: element.duration,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
        />
      ))}
      
      {/* Floating music notes */}
      {musicElements.map((note) => (
        <MusicNote
          key={note.id}
          x={note.x}
          y={note.y}
          animate={{
            y: [note.y, note.y - 20, note.y],
            rotate: [0, 180, 360],
            opacity: [0.7, 1, 0.7],
          }}
          transition={{
            duration: note.duration,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
        >
          {note.emoji}
        </MusicNote>
      ))}
    </EffectsContainer>
  )
}

export default BackgroundEffects
