import React, { useEffect, useState } from 'react'
import styled from '@emotion/styled'
import { motion, AnimatePresence } from 'framer-motion'
import { useGameStore } from '@/store/gameStore'

interface Heart {
  id: string
  emoji: string
  x: number
  color: string
  duration: number
  delay: number
}

const HeartsContainer = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
  overflow: hidden;
`

const FloatingHeart = styled(motion.div)<{ x: number; color: string }>`
  position: absolute;
  left: ${({ x }) => x}%;
  font-size: clamp(20px, 4vw, 35px);
  color: ${({ color }) => color};
  user-select: none;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
`

const heartEmojis = ['💖', '💕', '💗', '💓', '🌸', '🌺', '🦋', '✨', '💫', '🌟']
const heartColors = [
  '#ff69b4', '#ff1493', '#ff6347', '#ffd700', 
  '#ff8c00', '#da70d6', '#9370db', '#87ceeb'
]

export const FloatingHearts: React.FC = () => {
  const [hearts, setHearts] = useState<Heart[]>([])
  const { settings } = useGameStore()

  useEffect(() => {
    if (!settings.animationsEnabled) return

    const createHeart = () => {
      const heart: Heart = {
        id: Math.random().toString(36).substr(2, 9),
        emoji: heartEmojis[Math.floor(Math.random() * heartEmojis.length)],
        x: Math.random() * 100,
        color: heartColors[Math.floor(Math.random() * heartColors.length)],
        duration: Math.random() * 4 + 6, // 6-10 seconds
        delay: Math.random() * 2, // 0-2 seconds delay
      }

      setHearts(prev => [...prev, heart])

      // Remove heart after animation completes
      setTimeout(() => {
        setHearts(prev => prev.filter(h => h.id !== heart.id))
      }, (heart.duration + heart.delay) * 1000)
    }

    // Create initial hearts
    for (let i = 0; i < 3; i++) {
      setTimeout(createHeart, i * 1000)
    }

    // Create new hearts periodically
    const interval = setInterval(createHeart, 2000)

    return () => clearInterval(interval)
  }, [settings.animationsEnabled])

  if (!settings.animationsEnabled) return null

  return (
    <HeartsContainer>
      <AnimatePresence>
        {hearts.map((heart) => (
          <FloatingHeart
            key={heart.id}
            x={heart.x}
            color={heart.color}
            initial={{
              y: '100vh',
              opacity: 0,
              scale: 0.5,
              rotate: 0,
            }}
            animate={{
              y: '-100px',
              opacity: [0, 1, 1, 0],
              scale: [0.5, 1, 1, 0.5],
              rotate: 360,
            }}
            transition={{
              duration: heart.duration,
              delay: heart.delay,
              ease: 'easeOut',
              opacity: {
                times: [0, 0.1, 0.9, 1],
                duration: heart.duration,
              },
            }}
          >
            {heart.emoji}
          </FloatingHeart>
        ))}
      </AnimatePresence>
    </HeartsContainer>
  )
}

export default FloatingHearts
