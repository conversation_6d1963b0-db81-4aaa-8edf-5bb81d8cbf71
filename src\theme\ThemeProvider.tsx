import React from 'react'
import { ThemeProvider as EmotionThemeProvider } from '@emotion/react'
import { Global, css } from '@emotion/react'
import { theme } from './index'

interface ThemeProviderProps {
  children: React.ReactNode
}

const globalStyles = css`
  @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap');

  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  html {
    font-size: 16px;
    scroll-behavior: smooth;
  }

  body {
    font-family: ${theme.typography.fonts.body};
    font-size: ${theme.typography.fontSizes.base};
    font-weight: ${theme.typography.fontWeights.normal};
    line-height: ${theme.typography.lineHeights.normal};
    color: ${theme.colors.neutral[800]};
    background: ${theme.colors.gradients.dreamy};
    min-height: 100vh;
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  #root {
    min-height: 100vh;
    position: relative;
  }

  /* Focus styles for accessibility */
  *:focus {
    outline: 2px solid ${theme.colors.blushPink[500]};
    outline-offset: 2px;
  }

  /* Remove focus outline for mouse users */
  *:focus:not(:focus-visible) {
    outline: none;
  }

  /* Smooth transitions for interactive elements */
  button,
  a,
  input,
  textarea,
  select {
    transition: all ${theme.animations.durations.normal} ${theme.animations.easings.easeInOut};
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: ${theme.colors.neutral[100]};
  }

  ::-webkit-scrollbar-thumb {
    background: ${theme.colors.blushPink[300]};
    border-radius: ${theme.borderRadius.full};
  }

  ::-webkit-scrollbar-thumb:hover {
    background: ${theme.colors.blushPink[400]};
  }

  /* Selection styles */
  ::selection {
    background: ${theme.colors.blushPink[200]};
    color: ${theme.colors.blushPink[800]};
  }

  /* Disable text selection on interactive elements */
  button,
  .no-select {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  /* Responsive images */
  img {
    max-width: 100%;
    height: auto;
  }

  /* Remove default button styles */
  button {
    border: none;
    background: none;
    cursor: pointer;
    font-family: inherit;
  }

  /* Remove default link styles */
  a {
    color: inherit;
    text-decoration: none;
  }

  /* Accessibility improvements */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    body {
      background: white;
      color: black;
    }
  }
`

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  return (
    <EmotionThemeProvider theme={theme}>
      <Global styles={globalStyles} />
      {children}
    </EmotionThemeProvider>
  )
}

export default ThemeProvider
