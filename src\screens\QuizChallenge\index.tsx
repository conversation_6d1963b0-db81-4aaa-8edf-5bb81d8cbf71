import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import styled from '@emotion/styled'
import { motion, AnimatePresence } from 'framer-motion'
import { Card, Button, Typography } from '@/components/ui'
import { useGameStore } from '@/store/gameStore'

const QuizContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 120px);
  padding: ${({ theme }) => theme.spacing[4]};
  margin-top: 80px;
`

const QuizCard = styled(Card)`
  max-width: 700px;
  width: 100%;
  text-align: center;
`

const QuestionCard = styled(motion.div)`
  margin: ${({ theme }) => theme.spacing[6]} 0;
  padding: ${({ theme }) => theme.spacing[6]};
  background: rgba(255, 255, 255, 0.1);
  border-radius: ${({ theme }) => theme.borderRadius['2xl']};
  border: 2px solid rgba(255, 255, 255, 0.2);
`

const OptionsGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: ${({ theme }) => theme.spacing[3]};
  margin: ${({ theme }) => theme.spacing[6]} 0;

  ${({ theme }) => theme.mediaQueries.mobile} {
    grid-template-columns: 1fr;
  }
`

const OptionButton = styled(Button)<{ selected?: boolean; correct?: boolean; wrong?: boolean }>`
  padding: ${({ theme }) => theme.spacing[4]};
  text-align: left;
  justify-content: flex-start;
  min-height: 60px;
  
  ${({ selected, theme }) => selected && `
    background: ${theme.colors.blushPink[200]};
    border: 2px solid ${theme.colors.blushPink[400]};
  `}
  
  ${({ correct, theme }) => correct && `
    background: ${theme.colors.mintGreen[200]};
    border: 2px solid ${theme.colors.mintGreen[500]};
  `}
  
  ${({ wrong, theme }) => wrong && `
    background: ${theme.colors.neutral[200]};
    border: 2px solid ${theme.colors.neutral[400]};
  `}
`

const ProgressBar = styled.div`
  width: 100%;
  height: 8px;
  background: ${({ theme }) => theme.colors.neutral[200]};
  border-radius: ${({ theme }) => theme.borderRadius.full};
  margin-bottom: ${({ theme }) => theme.spacing[4]};
  overflow: hidden;
`

const ProgressFill = styled(motion.div)`
  height: 100%;
  background: ${({ theme }) => theme.colors.gradients.romantic};
  border-radius: ${({ theme }) => theme.borderRadius.full};
`

interface QuizQuestion {
  id: number
  question: string
  options: { id: string; text: string; emoji: string }[]
  correctAnswer: string
  explanation: string
}

const quizQuestions: QuizQuestion[] = [
  {
    id: 1,
    question: "What was the first thing that made me fall for you? 💕",
    options: [
      { id: 'a', text: 'Your beautiful smile', emoji: '😊' },
      { id: 'b', text: 'Your kind heart', emoji: '💖' },
      { id: 'c', text: 'Your sense of humor', emoji: '😄' },
      { id: 'd', text: 'Everything about you!', emoji: '✨' }
    ],
    correctAnswer: 'd',
    explanation: "It was everything about you that captured my heart! 💕"
  },
  {
    id: 2,
    question: "What's our favorite activity together? 🌟",
    options: [
      { id: 'a', text: 'Watching movies', emoji: '🎬' },
      { id: 'b', text: 'Long walks and talks', emoji: '🚶‍♀️' },
      { id: 'c', text: 'Cooking together', emoji: '👩‍🍳' },
      { id: 'd', text: 'Just being together', emoji: '🤗' }
    ],
    correctAnswer: 'd',
    explanation: "Every moment with you is perfect, no matter what we're doing! 🥰"
  },
  {
    id: 3,
    question: "What makes our relationship special? 💫",
    options: [
      { id: 'a', text: 'We understand each other', emoji: '🤝' },
      { id: 'b', text: 'We laugh together', emoji: '😂' },
      { id: 'c', text: 'We support each other', emoji: '💪' },
      { id: 'd', text: 'All of the above!', emoji: '🌈' }
    ],
    correctAnswer: 'd',
    explanation: "Our love is built on understanding, laughter, and support! 🌈"
  }
]

export const QuizChallengeScreen: React.FC = () => {
  const [currentQuestion, setCurrentQuestion] = useState(0)
  const [selectedAnswer, setSelectedAnswer] = useState<string | null>(null)
  const [showResult, setShowResult] = useState(false)
  const [score, setScore] = useState(0)
  const [quizComplete, setQuizComplete] = useState(false)
  
  const navigate = useNavigate()
  const { setCurrentLevel, completeLevel, setQuizScore } = useGameStore()

  const handleAnswerSelect = (answerId: string) => {
    if (showResult) return
    setSelectedAnswer(answerId)
  }

  const handleSubmitAnswer = () => {
    if (!selectedAnswer) return
    
    setShowResult(true)
    
    if (selectedAnswer === quizQuestions[currentQuestion].correctAnswer) {
      setScore(prev => prev + 1)
    }
    
    setTimeout(() => {
      if (currentQuestion < quizQuestions.length - 1) {
        setCurrentQuestion(prev => prev + 1)
        setSelectedAnswer(null)
        setShowResult(false)
      } else {
        setQuizComplete(true)
      }
    }, 2000)
  }

  const handleContinue = () => {
    completeLevel('quiz-challenge', score)
    setQuizScore(score)
    setCurrentLevel('photo-booth')
    navigate('/photo-booth')
  }

  const progress = ((currentQuestion + 1) / quizQuestions.length) * 100

  if (quizComplete) {
    return (
      <QuizContainer>
        <QuizCard variant="romantic" padding="xl">
          <Typography variant="h2" gradient>
            Quiz Complete! 🎉
          </Typography>
          
          <Typography variant="h3" style={{ margin: '2rem 0' }}>
            Score: {score}/{quizQuestions.length}
          </Typography>
          
          <Typography variant="body" size="lg" style={{ margin: '2rem 0' }}>
            {score === quizQuestions.length 
              ? "Perfect! You know our love story so well! 💕" 
              : "Great job! Our love grows stronger every day! 🌱"
            }
          </Typography>
          
          <Button variant="romantic" size="lg" onClick={handleContinue}>
            Continue to Photo Memories! 📸
          </Button>
        </QuizCard>
      </QuizContainer>
    )
  }

  const question = quizQuestions[currentQuestion]

  return (
    <QuizContainer>
      <QuizCard variant="dreamy" padding="xl">
        <Typography variant="h2" gradient>
          💝 Love Quiz Challenge
        </Typography>
        
        <ProgressBar>
          <ProgressFill
            initial={{ width: 0 }}
            animate={{ width: `${progress}%` }}
            transition={{ duration: 0.5 }}
          />
        </ProgressBar>
        
        <Typography variant="caption">
          Question {currentQuestion + 1} of {quizQuestions.length}
        </Typography>

        <AnimatePresence mode="wait">
          <QuestionCard
            key={currentQuestion}
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -50 }}
            transition={{ duration: 0.5 }}
          >
            <Typography variant="h3" style={{ marginBottom: '2rem' }}>
              {question.question}
            </Typography>

            <OptionsGrid>
              {question.options.map((option) => (
                <OptionButton
                  key={option.id}
                  variant="secondary"
                  selected={selectedAnswer === option.id}
                  correct={showResult && option.id === question.correctAnswer}
                  wrong={showResult && selectedAnswer === option.id && option.id !== question.correctAnswer}
                  onClick={() => handleAnswerSelect(option.id)}
                  disabled={showResult}
                >
                  <span style={{ marginRight: '0.5rem' }}>{option.emoji}</span>
                  {option.text}
                </OptionButton>
              ))}
            </OptionsGrid>

            {showResult && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                style={{ marginTop: '2rem' }}
              >
                <Typography variant="body" color="#14b8a6">
                  {question.explanation}
                </Typography>
              </motion.div>
            )}

            {selectedAnswer && !showResult && (
              <Button 
                variant="romantic" 
                onClick={handleSubmitAnswer}
                style={{ marginTop: '2rem' }}
              >
                Submit Answer ✨
              </Button>
            )}
          </QuestionCard>
        </AnimatePresence>
      </QuizCard>
    </QuizContainer>
  )
}

export default QuizChallengeScreen
