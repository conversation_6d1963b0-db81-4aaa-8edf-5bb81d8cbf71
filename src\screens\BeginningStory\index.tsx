import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import styled from '@emotion/styled'
import { motion, AnimatePresence } from 'framer-motion'
import { Card, Button, Typography } from '@/components/ui'
import { useGameStore } from '@/store/gameStore'

const StoryContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 120px);
  padding: ${({ theme }) => theme.spacing[4]};
  margin-top: 80px;
`

const StoryCard = styled(Card)`
  max-width: 700px;
  width: 100%;
  text-align: center;
  min-height: 400px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
`

const StoryContent = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: ${({ theme }) => theme.spacing[4]} 0;
`

const StoryText = styled(motion.div)`
  margin: ${({ theme }) => theme.spacing[4]} 0;
  line-height: 1.8;
  
  .highlight {
    background: linear-gradient(45deg, #ff9a9e, #fecfef);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: ${({ theme }) => theme.typography.fontWeights.semibold};
  }

  .nepali {
    color: ${({ theme }) => theme.colors.lavender[600]};
    font-weight: ${({ theme }) => theme.typography.fontWeights.semibold};
  }
`

const NavigationButtons = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing[4]};
  justify-content: center;
  margin-top: ${({ theme }) => theme.spacing[6]};

  ${({ theme }) => theme.mediaQueries.mobile} {
    flex-direction: column;
    gap: ${({ theme }) => theme.spacing[3]};
  }
`

const ProgressDots = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing[2]};
  justify-content: center;
  margin: ${({ theme }) => theme.spacing[4]} 0;
`

const ProgressDot = styled(motion.div)<{ active: boolean }>`
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: ${({ active, theme }) => 
    active ? theme.colors.blushPink[500] : theme.colors.neutral[300]};
  cursor: pointer;
`

const storyPages = [
  {
    title: "The Beginning 🌱",
    content: (
      <>
        <span className="nepali">सुरुवात</span> - It all started with a simple message, a spark that would grow into something beautiful...
        <br /><br />
        From the very first conversation, I knew there was something <span className="highlight">special</span> about you. 
        Your laugh, your kindness, the way you see the world - everything about you made my heart skip a beat! 💓
      </>
    )
  },
  {
    title: "Growing Closer 🌸",
    content: (
      <>
        Day by day, our bond grew stronger. Like the <span className="highlight">rhododendrons</span> blooming in the Himalayas, 
        our love blossomed naturally and beautifully.
        <br /><br />
        <span className="nepali">बिस्तारै बिस्तारै</span> - Slowly but surely, you became my favorite person, 
        my safe space, my home. 🏠💕
      </>
    )
  },
  {
    title: "Our First Year 🎉",
    content: (
      <>
        365 days of laughter, adventures, inside jokes, and countless <span className="highlight">मिठो</span> moments! 
        <br /><br />
        Through every season, every challenge, every celebration - we've grown together, 
        learned together, and loved deeper than I ever thought possible. 
        <br /><br />
        <span className="nepali">तिमी मेरो सबै कुरा हौ</span> - You are my everything! ✨
      </>
    )
  }
]

export const BeginningStoryScreen: React.FC = () => {
  const [currentPage, setCurrentPage] = useState(0)
  const navigate = useNavigate()
  const { setCurrentLevel, completeLevel } = useGameStore()

  const handleNext = () => {
    if (currentPage < storyPages.length - 1) {
      setCurrentPage(currentPage + 1)
    } else {
      completeLevel('beginning-story', 5)
      setCurrentLevel('memory-game')
      navigate('/memory-game')
    }
  }

  const handlePrevious = () => {
    if (currentPage > 0) {
      setCurrentPage(currentPage - 1)
    }
  }

  const handleDotClick = (index: number) => {
    setCurrentPage(index)
  }

  return (
    <StoryContainer>
      <StoryCard variant="dreamy" padding="xl">
        <StoryContent>
          <Typography variant="h2" gradient animate>
            {storyPages[currentPage].title}
          </Typography>

          <ProgressDots>
            {storyPages.map((_, index) => (
              <ProgressDot
                key={index}
                active={index === currentPage}
                onClick={() => handleDotClick(index)}
                whileHover={{ scale: 1.2 }}
                whileTap={{ scale: 0.9 }}
              />
            ))}
          </ProgressDots>

          <AnimatePresence mode="wait">
            <StoryText
              key={currentPage}
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -50 }}
              transition={{ duration: 0.5 }}
            >
              <Typography variant="body" size="lg">
                {storyPages[currentPage].content}
              </Typography>
            </StoryText>
          </AnimatePresence>
        </StoryContent>

        <NavigationButtons>
          {currentPage > 0 && (
            <Button 
              variant="secondary" 
              onClick={handlePrevious}
              aria-label="Go to previous story page"
            >
              ← Previous
            </Button>
          )}
          
          <Button 
            variant="romantic" 
            onClick={handleNext}
            aria-label={currentPage < storyPages.length - 1 ? "Go to next story page" : "Continue to memory game"}
          >
            {currentPage < storyPages.length - 1 ? 'Next →' : 'Continue Adventure! 🎮'}
          </Button>
        </NavigationButtons>
      </StoryCard>
    </StoryContainer>
  )
}

export default BeginningStoryScreen
