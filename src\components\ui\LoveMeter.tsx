import React from 'react'
import styled from '@emotion/styled'
import { motion } from 'framer-motion'
import { useGameStore } from '@/store/gameStore'
import { Typography } from './Typography'

const MeterContainer = styled(motion.div)`
  width: 100%;
  background: rgba(255, 255, 255, 0.9);
  border-radius: ${({ theme }) => theme.borderRadius['2xl']};
  padding: ${({ theme }) => theme.spacing[3]};
  box-shadow: ${({ theme }) => theme.shadows.lg};
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.3);
`

const MeterTrack = styled.div`
  width: 100%;
  height: 30px;
  background: linear-gradient(45deg, #ff9a9e, #fecfef);
  border-radius: ${({ theme }) => theme.borderRadius['2xl']};
  position: relative;
  overflow: hidden;
  box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.2);
  margin-top: ${({ theme }) => theme.spacing[2]};
`

const MeterFill = styled(motion.div)<{ progress: number }>`
  height: 100%;
  background: ${({ theme }) => theme.colors.gradients.romantic};
  border-radius: ${({ theme }) => theme.borderRadius['2xl']};
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: ${({ theme }) => theme.typography.fontWeights.bold};
  font-size: ${({ theme }) => theme.typography.fontSizes.sm};
  position: relative;
  overflow: hidden;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
    animation: shimmer 2s infinite;
  }

  @keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
  }
`

const LevelText = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: ${({ theme }) => theme.spacing[1]};
`

const ProgressText = styled(Typography)`
  font-size: ${({ theme }) => theme.typography.fontSizes.xs};
  color: ${({ theme }) => theme.colors.neutral[600]};
`

const levelNames = {
  'welcome': 'सुरुवात (Beginning)',
  'beginning-story': 'बढ्दो (Growing)',
  'memory-game': 'गहिरो (Deepening)',
  'quiz-challenge': 'परीक्षा (Testing)',
  'photo-booth': 'यादहरू (Memories)',
  'celebration': 'अनन्त माया (Infinite Love)',
}

const levelOrder: Array<keyof typeof levelNames> = [
  'welcome',
  'beginning-story', 
  'memory-game',
  'quiz-challenge',
  'photo-booth',
  'celebration'
]

export const LoveMeter: React.FC = () => {
  const { currentLevel, levelProgress } = useGameStore()
  
  const currentLevelIndex = levelOrder.indexOf(currentLevel)
  const completedLevels = Object.values(levelProgress).filter(p => p.isComplete).length
  const progress = Math.min((completedLevels / levelOrder.length) * 100, 100)
  
  const currentLevelName = levelNames[currentLevel] || 'माया Level'

  return (
    <MeterContainer
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <LevelText>
        <Typography variant="caption" weight="semibold" nepali>
          {currentLevelName}
        </Typography>
        <ProgressText>
          {completedLevels}/{levelOrder.length} Complete
        </ProgressText>
      </LevelText>
      
      <MeterTrack>
        <MeterFill
          progress={progress}
          initial={{ width: 0 }}
          animate={{ width: `${progress}%` }}
          transition={{ 
            duration: 1, 
            ease: 'easeOut',
            delay: 0.3 
          }}
        >
          {progress === 100 ? '∞ माया' : `${Math.round(progress)}%`}
        </MeterFill>
      </MeterTrack>
    </MeterContainer>
  )
}

export default LoveMeter
