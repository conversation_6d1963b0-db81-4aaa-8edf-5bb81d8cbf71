import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import styled from '@emotion/styled'
import { motion, AnimatePresence } from 'framer-motion'
import { Card, Button, Typography } from '@/components/ui'
import { useGameStore } from '@/store/gameStore'

const PhotoContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 120px);
  padding: ${({ theme }) => theme.spacing[4]};
  margin-top: 80px;
`

const PhotoCard = styled(Card)`
  max-width: 800px;
  width: 100%;
  text-align: center;
`

const PhotoGallery = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: ${({ theme }) => theme.spacing[4]};
  margin: ${({ theme }) => theme.spacing[6]} 0;
`

const PhotoFrame = styled(motion.div)<{ active: boolean }>`
  aspect-ratio: 1;
  background: ${({ theme }) => theme.colors.gradients.sunset};
  border-radius: ${({ theme }) => theme.borderRadius['2xl']};
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  border: 3px solid ${({ active, theme }) => 
    active ? theme.colors.blushPink[500] : 'transparent'};
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
  }
`

const PhotoContent = styled.div`
  position: relative;
  z-index: 1;
  text-align: center;
  padding: ${({ theme }) => theme.spacing[4]};
`

const PhotoEmoji = styled.div`
  font-size: 3rem;
  margin-bottom: ${({ theme }) => theme.spacing[2]};
  
  ${({ theme }) => theme.mediaQueries.mobile} {
    font-size: 2rem;
  }
`

const PhotoTitle = styled(Typography)`
  color: white;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
  margin-bottom: ${({ theme }) => theme.spacing[1]};
`

const PhotoSubtitle = styled(Typography)`
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
`

const NavigationControls = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing[4]};
  justify-content: center;
  margin-top: ${({ theme }) => theme.spacing[6]};

  ${({ theme }) => theme.mediaQueries.mobile} {
    flex-direction: column;
    gap: ${({ theme }) => theme.spacing[3]};
  }
`

interface PhotoMemory {
  id: string
  emoji: string
  title: string
  subtitle: string
  description: string
}

const photoMemories: PhotoMemory[] = [
  {
    id: 'first-date',
    emoji: '☕',
    title: 'First Coffee Date',
    subtitle: 'Nervous butterflies',
    description: 'The day we first met in person - I was so nervous but you made me feel so comfortable! ☕💕'
  },
  {
    id: 'first-laugh',
    emoji: '😂',
    title: 'First Big Laugh',
    subtitle: 'Your infectious joy',
    description: 'When you laughed so hard at my silly joke - that\'s when I knew you were special! 😄✨'
  },
  {
    id: 'first-adventure',
    emoji: '🌄',
    title: 'First Adventure',
    subtitle: 'Exploring together',
    description: 'Our first little adventure together - every step was more fun with you by my side! 🥾💫'
  },
  {
    id: 'cozy-moments',
    emoji: '🏠',
    title: 'Cozy Evenings',
    subtitle: 'Home together',
    description: 'Those perfect quiet evenings, just us, feeling completely at peace together. 🕯️💕'
  },
  {
    id: 'celebrations',
    emoji: '🎉',
    title: 'Celebrations',
    subtitle: 'Special occasions',
    description: 'Every celebration is better with you - birthdays, holidays, or just random Tuesday victories! 🎊🥳'
  },
  {
    id: 'future-dreams',
    emoji: '🌟',
    title: 'Future Dreams',
    subtitle: 'Planning together',
    description: 'All our talks about the future, dreams we want to chase together. The best is yet to come! 🚀💖'
  }
]

export const PhotoBoothScreen: React.FC = () => {
  const [selectedPhoto, setSelectedPhoto] = useState<string | null>(null)
  const [viewedPhotos, setViewedPhotos] = useState<Set<string>>(new Set())
  
  const navigate = useNavigate()
  const { setCurrentLevel, completeLevel, setCurrentPhotoIndex } = useGameStore()

  const handlePhotoClick = (photoId: string) => {
    setSelectedPhoto(photoId)
    setViewedPhotos(prev => new Set([...prev, photoId]))
    const photoIndex = photoMemories.findIndex(p => p.id === photoId)
    setCurrentPhotoIndex(photoIndex)
  }

  const handleContinue = () => {
    const score = viewedPhotos.size
    completeLevel('photo-booth', score)
    setCurrentLevel('celebration')
    navigate('/celebration')
  }

  const selectedPhotoData = selectedPhoto 
    ? photoMemories.find(p => p.id === selectedPhoto)
    : null

  return (
    <PhotoContainer>
      <PhotoCard variant="glass" padding="xl">
        <Typography variant="h2" gradient>
          📸 Our Memory Gallery
        </Typography>
        
        <Typography variant="body" style={{ margin: '1rem 0' }}>
          <span style={{ color: '#9d6bff', fontWeight: 600 }}>तस्बिरहरू</span> - 
          Click on each memory to relive our beautiful moments together!
        </Typography>

        <Typography variant="caption" color="#666">
          Viewed: {viewedPhotos.size}/{photoMemories.length} memories
        </Typography>

        <PhotoGallery>
          {photoMemories.map((photo) => (
            <PhotoFrame
              key={photo.id}
              active={selectedPhoto === photo.id}
              onClick={() => handlePhotoClick(photo.id)}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
            >
              <PhotoContent>
                <PhotoEmoji>{photo.emoji}</PhotoEmoji>
                <PhotoTitle variant="h5" weight="semibold">
                  {photo.title}
                </PhotoTitle>
                <PhotoSubtitle variant="caption">
                  {photo.subtitle}
                </PhotoSubtitle>
                {viewedPhotos.has(photo.id) && (
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    style={{ 
                      position: 'absolute', 
                      top: '10px', 
                      right: '10px',
                      fontSize: '1.5rem'
                    }}
                  >
                    ✅
                  </motion.div>
                )}
              </PhotoContent>
            </PhotoFrame>
          ))}
        </PhotoGallery>

        <AnimatePresence>
          {selectedPhotoData && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              style={{
                background: 'rgba(255, 255, 255, 0.95)',
                padding: '2rem',
                borderRadius: '1rem',
                margin: '2rem 0',
                border: '2px solid rgba(255, 255, 255, 0.3)'
              }}
            >
              <Typography variant="h4" gradient style={{ marginBottom: '1rem' }}>
                {selectedPhotoData.emoji} {selectedPhotoData.title}
              </Typography>
              <Typography variant="body">
                {selectedPhotoData.description}
              </Typography>
            </motion.div>
          )}
        </AnimatePresence>

        <NavigationControls>
          <Button 
            variant="romantic" 
            size="lg" 
            onClick={handleContinue}
            disabled={viewedPhotos.size === 0}
          >
            {viewedPhotos.size === photoMemories.length 
              ? 'Complete Journey! 🎊' 
              : `Continue (${viewedPhotos.size}/${photoMemories.length}) 🚀`
            }
          </Button>
        </NavigationControls>
      </PhotoCard>
    </PhotoContainer>
  )
}

export default PhotoBoothScreen
