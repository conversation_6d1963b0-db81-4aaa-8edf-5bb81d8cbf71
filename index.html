<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>💖 हाम्रो माया यात्रा - 1st Anniversary Special 💖</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(45deg, #ff6b6b, #feca57, #48dbfb, #bd79ff, #ff9ff3, #54a0ff);
            background-size: 600% 600%;
            animation: gradientShift 12s ease infinite;
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><text y="20" font-size="20" fill="rgba(255,255,255,0.1)">🌸</text><text x="30" y="40" font-size="15" fill="rgba(255,255,255,0.1)">🦋</text><text x="60" y="30" font-size="18" fill="rgba(255,255,255,0.1)">✨</text><text x="20" y="70" font-size="16" fill="rgba(255,255,255,0.1)">🌺</text><text x="80" y="80" font-size="14" fill="rgba(255,255,255,0.1)">💫</text></svg>') repeat;
            pointer-events: none;
            z-index: -1;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            25% { background-position: 50% 0%; }
            50% { background-position: 100% 50%; }
            75% { background-position: 50% 100%; }
            100% { background-position: 0% 50%; }
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            position: relative;
        }

        .game-screen {
            background: rgba(255, 255, 255, 0.98);
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 25px 60px rgba(0, 0, 0, 0.4);
            text-align: center;
            backdrop-filter: blur(20px);
            border: 3px solid rgba(255, 255, 255, 0.3);
            position: relative;
            overflow: hidden;
        }

        .game-screen::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: conic-gradient(from 0deg, transparent, rgba(255, 182, 193, 0.3), transparent);
            animation: rotate 20s linear infinite;
            z-index: -1;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .hearts {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            overflow: hidden;
            z-index: 1000;
        }

        .heart {
            position: absolute;
            font-size: 25px;
            animation: float 8s ease-in-out infinite;
        }

        .heart:nth-child(odd) {
            animation-delay: -2s;
        }

        .heart:nth-child(even) {
            animation-delay: -4s;
        }

        @keyframes float {
            0% {
                transform: translateY(100vh) rotate(0deg) scale(0.5);
                opacity: 0;
            }
            10% {
                opacity: 1;
                transform: translateY(90vh) rotate(45deg) scale(1);
            }
            90% {
                opacity: 1;
                transform: translateY(-50px) rotate(315deg) scale(1);
            }
            100% {
                transform: translateY(-100px) rotate(360deg) scale(0.5);
                opacity: 0;
            }
        }

        h1 {
            background: linear-gradient(45deg, #e74c3c, #f39c12, #e91e63, #9b59b6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 3em;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            animation: pulse 3s ease-in-out infinite alternate;
            font-weight: 700;
        }

        @keyframes pulse {
            from { transform: scale(1); }
            to { transform: scale(1.05); }
        }

        .level-title {
            color: #8e44ad;
            font-size: 1.8em;
            margin-bottom: 15px;
        }

        .question {
            font-size: 1.3em;
            color: #2c3e50;
            margin: 20px 0;
            line-height: 1.5;
        }

        .options {
            display: grid;
            gap: 15px;
            margin: 30px 0;
        }

        .option-btn {
            background: linear-gradient(45deg, #ff6b6b, #feca57, #ff8a80);
            border: none;
            border-radius: 20px;
            padding: 18px 30px;
            font-size: 1.2em;
            font-weight: 600;
            color: white;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            position: relative;
            overflow: hidden;
        }

        .option-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            transition: left 0.5s;
        }

        .option-btn:hover::before {
            left: 100%;
        }

        .option-btn:hover {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4);
        }

        .option-btn:active {
            transform: translateY(-2px) scale(1.02);
        }

        .mini-game {
            margin: 30px 0;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 20px 0;
        }

        canvas {
            border: 3px solid #e74c3c;
            border-radius: 10px;
            background: white;
        }

        .score {
            font-size: 1.5em;
            color: #27ae60;
            font-weight: bold;
            margin: 15px 0;
        }

        .final-message {
            background: linear-gradient(45deg, #ff6b6b, #ff8a80);
            color: white;
            padding: 30px;
            border-radius: 20px;
            margin: 20px 0;
            font-size: 1.2em;
            line-height: 1.6;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .celebration {
            animation: celebrate 2s ease-in-out infinite;
        }

        @keyframes celebrate {
            0%, 100% { transform: rotate(0deg) scale(1); }
            25% { transform: rotate(-5deg) scale(1.05); }
            75% { transform: rotate(5deg) scale(1.05); }
        }

        .photo-memory {
            width: 200px;
            height: 200px;
            background: #f8f9fa;
            border: 3px solid #e74c3c;
            border-radius: 50%;
            margin: 20px auto;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3em;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .photo-memory:hover {
            transform: scale(1.1) rotate(10deg);
        }

        .memory-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #e74c3c;
            border-radius: 10px;
            font-size: 1.1em;
            margin: 15px 0;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #ecf0f1;
            border-radius: 10px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(45deg, #ff6b6b, #feca57);
            border-radius: 10px;
            transition: width 0.5s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        .hidden {
            display: none;
        }

        .sparkle {
            display: inline-block;
            animation: sparkle 2s ease-in-out infinite;
        }

        @keyframes sparkle {
            0%, 100% { opacity: 1; transform: scale(1) rotate(0deg); }
            25% { opacity: 0.7; transform: scale(1.3) rotate(90deg); }
            50% { opacity: 0.4; transform: scale(1.5) rotate(180deg); }
            75% { opacity: 0.7; transform: scale(1.3) rotate(270deg); }
        }

        .music-note {
            position: absolute;
            font-size: 30px;
            animation: musicFloat 4s ease-in-out infinite;
            pointer-events: none;
        }

        @keyframes musicFloat {
            0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.7; }
            50% { transform: translateY(-20px) rotate(180deg); opacity: 1; }
        }

        .cultural-element {
            font-size: 2.5em;
            margin: 10px;
            display: inline-block;
            animation: culturalPulse 3s ease-in-out infinite;
        }

        @keyframes culturalPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.2); }
        }

        .love-meter {
            width: 100%;
            height: 30px;
            background: linear-gradient(45deg, #ff9a9e, #fecfef);
            border-radius: 15px;
            margin: 20px 0;
            position: relative;
            overflow: hidden;
            box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.2);
        }

        .love-fill {
            height: 100%;
            background: linear-gradient(45deg, #ff6b6b, #feca57, #ff8a80);
            border-radius: 15px;
            transition: width 2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            position: relative;
        }

        .love-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .interactive-element {
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .interactive-element:hover {
            transform: scale(1.1) rotate(5deg);
            filter: brightness(1.2);
        }

        .nepali-text {
            font-family: 'Poppins', sans-serif;
            color: #8e44ad;
            font-weight: 600;
            text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
        }

        .floating-emoji {
            position: absolute;
            animation: emojiFloat 6s ease-in-out infinite;
            pointer-events: none;
        }

        @keyframes emojiFloat {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-15px) rotate(120deg); }
            66% { transform: translateY(-10px) rotate(240deg); }
        }

        .quiz-question {
            margin: 20px 0;
            padding: 20px;
            background: rgba(255, 182, 193, 0.1);
            border-radius: 15px;
            border: 2px solid rgba(255, 105, 180, 0.3);
        }

        .quiz-question h3 {
            color: #8e44ad;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .quiz-options {
            display: grid;
            gap: 10px;
        }

        .quiz-btn {
            background: linear-gradient(45deg, #ff8a80, #ffc1e3);
            border: none;
            border-radius: 15px;
            padding: 12px 20px;
            font-size: 1em;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .quiz-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
            background: linear-gradient(45deg, #ff6b6b, #ff8a80);
        }

        .quiz-progress {
            font-size: 1.2em;
            color: #e74c3c;
            font-weight: bold;
            margin: 20px 0;
        }

        .photobooth-container {
            max-width: 500px;
            margin: 30px auto;
            padding: 20px;
            background: linear-gradient(45deg, #ff9a9e, #fad0c4);
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
        }

        .photo-frame {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: inset 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .photo-display {
            width: 100%;
            height: 300px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            position: relative;
            overflow: hidden;
            transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        #mainPhoto {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 10px;
            transition: transform 0.3s ease;
        }

        .photo-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
            color: white;
            padding: 20px;
            text-align: center;
            transform: translateY(100%);
            transition: transform 0.3s ease;
        }

        .photo-display:hover .photo-overlay {
            transform: translateY(0);
        }

        .photo-display:hover #mainPhoto {
            transform: scale(1.05);
        }

        .photo-content {
            text-align: center;
            color: white;
            z-index: 2;
        }

        .photo-emoji {
            font-size: 5em;
            margin-bottom: 15px;
            animation: photoGlow 2s ease-in-out infinite alternate;
        }

        @keyframes photoGlow {
            from { text-shadow: 0 0 20px rgba(255, 255, 255, 0.5); }
            to { text-shadow: 0 0 30px rgba(255, 255, 255, 0.8); }
        }

        .photo-title {
            font-size: 1.8em;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .photo-subtitle {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .photo-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .photo-btn {
            background: linear-gradient(45deg, #ff6b6b, #feca57);
            border: none;
            border-radius: 25px;
            padding: 12px 20px;
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .photo-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }

        .photo-counter {
            font-size: 1.1em;
            font-weight: bold;
            color: #8e44ad;
        }

        .photo-strip {
            display: flex;
            justify-content: center;
            gap: 8px;
            flex-wrap: wrap;
        }

        .strip-photo {
            width: 40px;
            height: 40px;
            background: linear-gradient(45deg, #ffecd2, #fcb69f);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5em;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 3px solid transparent;
            overflow: hidden;
        }

        .strip-photo img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 5px;
        }

        .strip-photo:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .strip-photo.active {
            border-color: #e74c3c;
            background: linear-gradient(45deg, #ff6b6b, #feca57);
            transform: scale(1.15);
        }
    </style>
</head>
<body>
    <div class="hearts" id="hearts"></div>
    
    <div class="container">
        <div class="game-screen">
            <div class="love-meter">
                <div class="love-fill" id="loveMeter">माया Level ∞</div>
            </div>

            <!-- Welcome Screen -->
            <div id="welcome" class="screen">
                <h1 class="celebration">💖 हाम्रो माया यात्रा 💖</h1>
                <div class="cultural-element">🏔️ 🌸 🦋 ✨ 🌺</div>
                <div class="question">
                    <span class="nepali-text">मेरो प्रिय,</span> Welcome to our magical 1st Anniversary Adventure! 🎉<br><br>
                    Just like the beautiful mountains of Nepal, our love reaches new heights every day!<br>
                    Ready for a journey through our memories, मिठो moments, and surprises?<br><br>
                    <span class="cultural-element interactive-element">🕉️</span>
                </div>
                <div class="options">
                    <button class="option-btn" onclick="startGame()">
                        <span class="nepali-text">सुरु गरौं</span> - Let's Begin Our Adventure! 💕
                    </button>
                </div>
            </div>

            <!-- Level 1: Our Beginning Story -->
            <div id="level1" class="screen hidden">
                <h2 class="level-title">🚌 Our Beautiful Beginning</h2>
                <div class="cultural-element">🚌 📱 🎂 💕</div>
                <div class="question" id="question1">
                    Our love story started in the most sweet way!<br>
                    What was the magical sequence that brought us together?<br><br>
                    <div class="floating-emoji" style="top: 10px; right: 20px;">🌸</div>
                </div>
                <div class="options">
                    <button class="option-btn" onclick="answerQuestion(1, 'bus')">
                        We met in the bus like a movie scene! 🚌✨
                    </button>
                    <button class="option-btn" onclick="answerQuestion(1, 'instagram')">
                        You followed me on Instagram (smooth move! 😏) 📱💕
                    </button>
                    <button class="option-btn" onclick="answerQuestion(1, 'birthday')">
                        You wished me birthday and my heart melted! 🎂💖
                    </button>
                    <button class="option-btn" onclick="answerQuestion(1, 'comfort')">
                        I comforted you and you fell for me! 🥰💕
                    </button>
                </div>
            </div>

            <!-- Level 2: Our Epic Date Adventures -->
            <div id="level2" class="screen hidden">
                <h2 class="level-title">🎬 Our Legendary Date Adventures</h2>
                <div class="cultural-element">🎬 ☕ 👻 🎢 🍦</div>
                <div class="question">
                    Click on the floating memories to capture them!<br>
                    From movie halls to haunted houses - we've been through it all! 😂💕<br>
                    Catch all 10 memories to unlock the next level! 🎵
                </div>
                <div class="score">Memories Captured: <span id="danceScore">0</span>/10 🥰</div>
                <div class="canvas-container">
                    <canvas id="gameCanvas" width="450" height="350"></canvas>
                </div>
                <div class="mini-game">
                    <button class="option-btn" id="danceGameBtn" onclick="startMemoryGame()">
                        Capture Our Romance Memories! 💕🎬
                    </button>
                </div>
            </div>

            <!-- Level 3: Love Quiz Challenge -->
            <div id="level3" class="screen hidden">
                <h2 class="level-title">💕 Love Quiz Challenge</h2>
                <div class="cultural-element">💝 💏 🥰 💖 🔥</div>
                <div class="question">
                    Answer these questions about our sweetest moments together!<br>
                    Show me how well you remember our love story! 💕
                </div>
                <div id="quizContainer">
                    <div class="quiz-question" id="quiz1">
                        <h3>Question 1: What's your favorite thing when we sleep together? 😴</h3>
                        <div class="quiz-options">
                            <button class="quiz-btn" onclick="answerQuiz(1, 'heat')">Sharing body heat and staying warm! 🔥</button>
                            <button class="quiz-btn" onclick="answerQuiz(1, 'cuddle')">Cuddling all night long! 🤗</button>
                            <button class="quiz-btn" onclick="answerQuiz(1, 'baby')">You treating me like your baby! 👶</button>
                        </div>
                    </div>
                    
                    <div class="quiz-question hidden" id="quiz2">
                        <h3>Question 2: What made our movie date extra special? 🎬</h3>
                        <div class="quiz-options">
                            <button class="quiz-btn" onclick="answerQuiz(2, 'kiss')">That sneaky kiss when lights went off! 💏</button>
                            <button class="quiz-btn" onclick="answerQuiz(2, 'chiya')">The chiya ghar afterwards! ☕</button>
                            <button class="quiz-btn" onclick="answerQuiz(2, 'mom')">Almost getting caught by your mom! 😅</button>
                        </div>
                    </div>
                    
                    <div class="quiz-question hidden" id="quiz3">
                        <h3>Question 3: Which adventure made you scream the most? 😱</h3>
                        <div class="quiz-options">
                            <button class="quiz-btn" onclick="answerQuiz(3, 'bhoot')">Bhoot ghar - you were terrified! 👻</button>
                            <button class="quiz-btn" onclick="answerQuiz(3, 'columbus')">Columbus ride - more screaming! 🎢</button>
                            <button class="quiz-btn" onclick="answerQuiz(3, 'both')">Both! You're such a scaredy cat! 😂</button>
                        </div>
                    </div>
                </div>
                <div class="quiz-progress">Quiz Progress: <span id="quizScore">0</span>/3 ✨</div>
            </div>

            <!-- Level 4: Memory Photobooth -->
            <div id="level4" class="screen hidden">
                <h2 class="level-title">📸 Memory Photobooth</h2>
                <div class="cultural-element">🍦 🧋 📺 💏 😴</div>
                <div class="question">
                    Step into our memory photobooth! Click through our special moments!<br>
                    Each photo tells the story of our amazing journey together! 💕
                </div>
                
                <div class="photobooth-container">
                    <div class="photo-frame">
                        <div class="photo-display" id="photoDisplay">
                            <img id="mainPhoto" src="photos/photo1.jpg" alt="Our Memory" />
                            <div class="photo-overlay">
                                <div class="photo-title">Bus Meeting</div>
                                <div class="photo-subtitle">Where it all began! ✨</div>
                            </div>
                        </div>
                        
                        <div class="photo-controls">
                            <button class="photo-btn" onclick="previousPhoto()">← Previous</button>
                            <span class="photo-counter">
                                <span id="currentPhoto">1</span> / <span id="totalPhotos">10</span>
                            </span>
                            <button class="photo-btn" onclick="nextPhoto()">Next →</button>
                        </div>
                        
                        <div class="photo-strip">
                            <div class="strip-photo active" data-index="0" onclick="selectPhoto(0)">
                                <img src="photos/photo1.jpg" alt="Photo 1" />
                            </div>
                            <div class="strip-photo" data-index="1" onclick="selectPhoto(1)">
                                <img src="photos/photo2.jpg" alt="Photo 2" />
                            </div>
                            <div class="strip-photo" data-index="2" onclick="selectPhoto(2)">
                                <img src="photos/photo3.jpg" alt="Photo 3" />
                            </div>
                            <div class="strip-photo" data-index="3" onclick="selectPhoto(3)">
                                <img src="photos/photo4.jpg" alt="Photo 4" />
                            </div>
                            <div class="strip-photo" data-index="4" onclick="selectPhoto(4)">
                                <img src="photos/photo5.jpg" alt="Photo 5" />
                            </div>
                            <div class="strip-photo" data-index="5" onclick="selectPhoto(5)">
                                <img src="photos/photo6.jpg" alt="Photo 6" />
                            </div>
                            <div class="strip-photo" data-index="6" onclick="selectPhoto(6)">
                                <img src="photos/photo7.jpg" alt="Photo 7" />
                            </div>
                            <div class="strip-photo" data-index="7" onclick="selectPhoto(7)">
                                <img src="photos/photo8.jpg" alt="Photo 8" />
                            </div>
                            <div class="strip-photo" data-index="8" onclick="selectPhoto(8)">
                                <img src="photos/photo9.jpg" alt="Photo 9" />
                            </div>
                            <div class="strip-photo" data-index="9" onclick="selectPhoto(9)">
                                <img src="photos/photo10.jpg" alt="Photo 10" />
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="options">
                    <button class="option-btn" onclick="completePhotobooth()">
                        Complete Our Memory Tour! ✨
                    </button>
                </div>
            </div>

            <!-- Final Level: Grand Celebration -->
            <div id="final" class="screen hidden">
                <h1 class="celebration">🎉 <span class="nepali-text">बधाई छ</span> CONGRATULATIONS! 🎉</h1>
                <div class="cultural-element">🏔️ 🌸 🦋 ✨ 🌺 🎊</div>
                <div class="final-message">
                    <div class="sparkle">✨</div> You've completed our <span class="nepali-text">माया यात्रा</span>! <div class="sparkle">✨</div><br><br>
                    
                    🚌 <strong>From Bus Ride to Forever Ride:</strong> 🚌<br>
                    • Started with a bus meeting (destiny much? 😍)<br>
                    • Instagram follow (smooth stalking skills! 😂)<br>
                    • Birthday wishes that melted my heart 🎂💕<br>
                    • You comforting me and stealing my heart completely! 💖<br><br>
                    
                    🎬 <strong>Our Epic Date Adventures:</strong> 🎬<br>
                    • Movie date + <span class="nepali-text">चिया घर</span> (and that kiss when lights went off! 😏💏)<br>
                    • Almost got caught by your mom (your lying skills need work! 😂)<br>
                    • Bhoot ghar adventures (you were scared AF! 👻😱)<br>
                    • Columbus ride (more screaming from you! 🎢😂)<br>
                    • Blueberry ice cream dates (your absolute favorite! 🍦💙)<br>
                    • Bubble tea adventures (so many flavors, so much love! 🧋)<br>
                    • Netflix & chill nights (hello spicy side! 😈🔥)<br><br>
                    
                    😴 <strong>Our Sweetest Moments:</strong> 😴<br>
                    • Sleeping together sharing body heat (best human heater ever! 🔥)<br>
                    • You treating me like your actual baby (so cute! 👶💕)<br>
                    • All those tender moments that made us "us" 🥰<br><br>
                    
                    💕 <strong>Happy 1st Anniversary, मेरो <span class="nepali-text">जान</span>!</strong> 💕<br><br>
                    
                    From that first bus ride to sharing warmth in bed,<br>
                    From scared movie kisses to Netflix adventures,<br>
                    From ice cream dates to bubble tea experiments,<br>
                    Every moment with you has been pure magic! ✨<br><br>
                    
                    <div style="font-size: 1.8em; color: #fff200; text-shadow: 2px 2px 4px rgba(0,0,0,0.3);">
                        🎮 <span class="nepali-text">खेल सकियो</span>: LOVE LEVEL ∞ 🎮
                    </div><br>
                    <div class="cultural-element">💑 हाम्रो कहानी सधैं जारी रहनेछ 💑</div>
                </div>
                <div class="options">
                    <button class="option-btn" onclick="playAgain()">
                        Play Our <span class="nepali-text">माया यात्रा</span> Again! 🔄
                    </button>
                    <button class="option-btn" onclick="showSurprise()">
                        <span class="nepali-text">अझै एक उपहार</span> - One More Surprise! 🎁
                    </button>
                    <button class="option-btn" onclick="createFireworks()">
                        Celebrate with <span class="nepali-text">आतिशबाजी</span>! 🎆
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentLevel = 0;
        let danceScore = 0;
        let gameActive = false;
        let musicNotes = ['🎵', '🎶', '💃', '🥻', '🎭'];

        // Create enhanced floating hearts with variety
        function createHeart() {
            const hearts = ['💖', '💕', '💗', '💓', '🌸', '🌺', '🦋', '✨'];
            const heart = document.createElement('div');
            heart.className = 'heart';
            heart.innerHTML = hearts[Math.random() * hearts.length | 0];
            heart.style.left = Math.random() * window.innerWidth + 'px';
            heart.style.animationDuration = (Math.random() * 4 + 6) + 's';
            heart.style.fontSize = (Math.random() * 15 + 20) + 'px';
            
            // Random colors for variety
            const colors = ['#ff69b4', '#ff1493', '#ff6347', '#ffd700', '#ff8c00', '#da70d6'];
            heart.style.color = colors[Math.random() * colors.length | 0];
            
            document.getElementById('hearts').appendChild(heart);
            
            setTimeout(() => {
                if (heart.parentNode) {
                    heart.parentNode.removeChild(heart);
                }
            }, 10000);
        }

        // Create hearts more frequently
        setInterval(createHeart, 600);

        // Add floating music notes
        function createMusicNote() {
            const note = document.createElement('div');
            note.className = 'music-note';
            note.innerHTML = musicNotes[Math.random() * musicNotes.length | 0];
            note.style.left = Math.random() * window.innerWidth + 'px';
            note.style.top = Math.random() * window.innerHeight + 'px';
            document.body.appendChild(note);
            
            setTimeout(() => {
                if (note.parentNode) {
                    note.parentNode.removeChild(note);
                }
            }, 4000);
        }

        setInterval(createMusicNote, 3000);

        function startGame() {
            currentLevel = 1;
            showLevel('level1');
            updateLoveMeter();
            playSound('start');
        }

        function showLevel(levelId) {
            document.querySelectorAll('.screen').forEach(screen => {
                screen.classList.add('hidden');
            });
            document.getElementById(levelId).classList.remove('hidden');
            
            // Initialize photobooth when level4 is shown
            if (levelId === 'level4') {
                currentPhotoIndex = 0;
                updatePhoto(0);
            }
        }

        function updateLoveMeter() {
            const loveMeter = document.getElementById('loveMeter');
            const progress = (currentLevel / 5) * 100;
            document.querySelector('.love-fill').style.width = progress + '%';
            
            const nepaliLevels = ['सुरुवात', 'बढ्दो', 'गहिरो', 'अनन्त', 'पूर्ण माया'];
            loveMeter.textContent = `${nepaliLevels[Math.min(currentLevel - 1, 4)]} Level ${currentLevel}`;
        }

        function answerQuestion(level, answer) {
            const responses = {
                'bus': 'Yes! That bus ride was destiny calling! Like a movie scene! 🚌✨',
                'instagram': 'Haha smooth move indeed! Best follow request ever! 📱💕',
                'birthday': 'That birthday wish was the beginning of forever! 🎂💖',
                'comfort': 'The moment I comforted you and you fell for me completely! 🥰💕'
            };
            
            setTimeout(() => {
                alert(responses[answer] || 'Perfect! Our story is truly beautiful! 💕');
                currentLevel = 2;
                showLevel('level2');
                updateLoveMeter();
                playSound('success');
            }, 500);
        }

        function startMemoryGame() {
            const canvas = document.getElementById('gameCanvas');
            const ctx = canvas.getContext('2d');
            const btn = document.getElementById('danceGameBtn');
            
            btn.style.display = 'none';
            danceScore = 0;
            gameActive = true;
            
            const memoryElements = ['🎬', '☕', '👻', '🎢', '🍦', '🧋', '📺', '💏', '🛏️', '👶'];
            const memories = [];
            const memoryTexts = [
                'Movie Date!', 'Chiya Ghar!', 'Bhoot Ghar!', 'Columbus Ride!', 
                'Blueberry Ice Cream!', 'Bubble Tea!', 'Netflix & Chill!', 'Sweet Kisses!', 'Cozy Sleep!', 'Baby Treatment!'
            ];
            
            // Add particle effects for captured memories
            const particles = [];
            
            canvas.addEventListener('click', (e) => {
                if (!gameActive) return;
                
                const rect = canvas.getBoundingClientRect();
                const clickX = e.clientX - rect.left;
                const clickY = e.clientY - rect.top;
                
                // Check if clicked on any memory
                for (let i = memories.length - 1; i >= 0; i--) {
                    const memory = memories[i];
                    const distance = Math.sqrt((clickX - memory.x) ** 2 + (clickY - memory.y) ** 2);
                    
                    if (distance <= 35) {
                        // Create capture effect
                        for (let p = 0; p < 8; p++) {
                            particles.push({
                                x: memory.x,
                                y: memory.y,
                                vx: (Math.random() - 0.5) * 8,
                                vy: (Math.random() - 0.5) * 8,
                                life: 30,
                                color: `hsl(${Math.random() * 360}, 70%, 60%)`
                            });
                        }
                        
                        memories.splice(i, 1);
                        danceScore++;
                        document.getElementById('danceScore').textContent = danceScore;
                        playSound('click');
                        
                        // Show memory text with enhanced animation
                        showMemoryMessage(memory.text);
                        
                        if (danceScore >= 10) {
                            gameActive = false;
                            setTimeout(() => {
                                alert('Amazing! All our beautiful memories captured! 💕🎬');
                                currentLevel = 3;
                                showLevel('level3');
                                updateLoveMeter();
                            }, 500);
                            return;
                        }
                    }
                }
            });

            function spawnMemory() {
                if (!gameActive || memories.length >= 6) return;
                const index = Math.floor(Math.random() * memoryElements.length);
                const side = Math.floor(Math.random() * 4); // 0=top, 1=right, 2=bottom, 3=left
                let x, y, vx, vy;
                
                switch(side) {
                    case 0: // top
                        x = Math.random() * canvas.width;
                        y = -30;
                        vx = (Math.random() - 0.5) * 2;
                        vy = Math.random() * 2 + 1;
                        break;
                    case 1: // right
                        x = canvas.width + 30;
                        y = Math.random() * canvas.height;
                        vx = -(Math.random() * 2 + 1);
                        vy = (Math.random() - 0.5) * 2;
                        break;
                    case 2: // bottom
                        x = Math.random() * canvas.width;
                        y = canvas.height + 30;
                        vx = (Math.random() - 0.5) * 2;
                        vy = -(Math.random() * 2 + 1);
                        break;
                    case 3: // left
                        x = -30;
                        y = Math.random() * canvas.height;
                        vx = Math.random() * 2 + 1;
                        vy = (Math.random() - 0.5) * 2;
                        break;
                }
                
                memories.push({
                    x: x,
                    y: y,
                    vx: vx,
                    vy: vy,
                    emoji: memoryElements[index],
                    text: memoryTexts[index],
                    life: 200 + Math.random() * 100,
                    scale: 0.8 + Math.random() * 0.4,
                    rotation: 0
                });
            }

            function showMemoryMessage(text) {
                const messageElement = document.createElement('div');
                messageElement.innerHTML = `💕 ${text} 💕`;
                messageElement.style.position = 'absolute';
                messageElement.style.left = '50%';
                messageElement.style.top = '20px';
                messageElement.style.transform = 'translateX(-50%) scale(0)';
                messageElement.style.background = 'linear-gradient(45deg, #ff6b6b, #feca57)';
                messageElement.style.padding = '12px 25px';
                messageElement.style.borderRadius = '20px';
                messageElement.style.fontSize = '18px';
                messageElement.style.fontWeight = 'bold';
                messageElement.style.color = 'white';
                messageElement.style.zIndex = '1000';
                messageElement.style.pointerEvents = 'none';
                messageElement.style.transition = 'transform 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55)';
                messageElement.style.boxShadow = '0 8px 25px rgba(0, 0, 0, 0.3)';
                document.body.appendChild(messageElement);
                
                setTimeout(() => {
                    messageElement.style.transform = 'translateX(-50%) scale(1)';
                }, 10);
                
                setTimeout(() => {
                    messageElement.style.transform = 'translateX(-50%) scale(0)';
                    setTimeout(() => {
                        if (messageElement.parentNode) {
                            messageElement.parentNode.removeChild(messageElement);
                        }
                    }, 500);
                }, 2000);
            }

            function gameLoop() {
                if (!gameActive) return;
                
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // Draw animated background
                const time = Date.now() * 0.001;
                for (let i = 0; i < 10; i++) {
                    ctx.fillStyle = `rgba(255, 182, 193, ${0.05 + Math.sin(time + i) * 0.02})`;
                    ctx.fillRect(i * 45, 0, 22, canvas.height);
                }
                
                // Update and draw particles
                for (let i = particles.length - 1; i >= 0; i--) {
                    const p = particles[i];
                    p.x += p.vx;
                    p.y += p.vy;
                    p.vx *= 0.95;
                    p.vy *= 0.95;
                    p.life--;
                    
                    ctx.fillStyle = p.color;
                    ctx.globalAlpha = p.life / 30;
                    ctx.fillRect(p.x - 2, p.y - 2, 4, 4);
                    
                    if (p.life <= 0) {
                        particles.splice(i, 1);
                    }
                }
                ctx.globalAlpha = 1;
                
                // Update and draw memories
                for (let i = memories.length - 1; i >= 0; i--) {
                    const memory = memories[i];
                    memory.life--;
                    memory.x += memory.vx;
                    memory.y += memory.vy;
                    memory.rotation += 0.02;
                    
                    // Bounce off walls
                    if (memory.x < 30 || memory.x > canvas.width - 30) memory.vx *= -0.8;
                    if (memory.y < 30 || memory.y > canvas.height - 30) memory.vy *= -0.8;
                    
                    // Keep in bounds
                    memory.x = Math.max(30, Math.min(canvas.width - 30, memory.x));
                    memory.y = Math.max(30, Math.min(canvas.height - 30, memory.y));
                    
                    // Draw memory with rotation and scaling
                    ctx.save();
                    ctx.translate(memory.x, memory.y);
                    ctx.rotate(memory.rotation);
                    ctx.scale(memory.scale, memory.scale);
                    ctx.font = '45px Arial';
                    ctx.textAlign = 'center';
                    ctx.textBaseline = 'middle';
                    ctx.fillText(memory.emoji, 0, 0);
                    ctx.restore();
                    
                    // Draw glow effect
                    ctx.shadowColor = '#ff69b4';
                    ctx.shadowBlur = 10;
                    ctx.shadowOffsetX = 0;
                    ctx.shadowOffsetY = 0;
                    
                    if (memory.life <= 0) {
                        memories.splice(i, 1);
                    }
                }
                
                ctx.shadowBlur = 0;
                
                // Spawn new memories
                if (Math.random() < 0.03) {
                    spawnMemory();
                }
                
                if (danceScore < 10) {
                    requestAnimationFrame(gameLoop);
                }
            }

            gameLoop();
            
            // Initial spawn
            for (let i = 0; i < 3; i++) {
                setTimeout(spawnMemory, i * 1000);
            }
        }

        let quizProgress = 0;
        let currentPhotoIndex = 0;
        
        const photoMemories = [
            {
                src: 'photos/photo1.jpg',
                title: 'Bus Meeting',
                subtitle: 'Where destiny brought us together! ✨'
            },
            {
                src: 'photos/photo2.jpg',
                title: 'Instagram Follow',
                subtitle: 'Smooth stalking skills activated! 😏'
            },
            {
                src: 'photos/photo3.jpg',
                title: 'Birthday Wish',
                subtitle: 'The message that melted my heart! 💖'
            },
            {
                src: 'photos/photo4.jpg',
                title: 'Movie Date',
                subtitle: 'Lights off, hearts on! 💏'
            },
            {
                src: 'photos/photo5.jpg',
                title: 'Bhoot Ghar',
                subtitle: 'You were so scared! 😂'
            },
            {
                src: 'photos/photo6.jpg',
                title: 'Columbus Ride',
                subtitle: 'Screaming champion! 🏆'
            },
            {
                src: 'photos/photo7.jpg',
                title: 'Blueberry Ice Cream',
                subtitle: 'Your absolute favorite! 💙'
            },
            {
                src: 'photos/photo8.jpg',
                title: 'Bubble Tea Dates',
                subtitle: 'So many flavors, so much love! 🥰'
            },
            {
                src: 'photos/photo9.jpg',
                title: 'Netflix & Chill',
                subtitle: 'Paradise with you! 🔥'
            },
            {
                src: 'photos/photo10.jpg',
                title: 'Cozy Sleep',
                subtitle: 'Best human heater ever! 💕'
            }
        ];
        
        function updatePhoto(index) {
            const photo = photoMemories[index];
            const mainPhoto = document.getElementById('mainPhoto');
            const photoOverlay = document.querySelector('.photo-overlay');
            
            // Update main photo
            mainPhoto.src = photo.src;
            mainPhoto.alt = photo.title;
            
            // Update overlay content
            photoOverlay.querySelector('.photo-title').textContent = photo.title;
            photoOverlay.querySelector('.photo-subtitle').textContent = photo.subtitle;
            
            // Update counter
            document.getElementById('currentPhoto').textContent = index + 1;
            document.getElementById('totalPhotos').textContent = photoMemories.length;
            
            // Update strip photos
            document.querySelectorAll('.strip-photo').forEach((strip, i) => {
                strip.classList.toggle('active', i === index);
            });
            
            // Add animation effect
            mainPhoto.style.transform = 'scale(0.95)';
            setTimeout(() => {
                mainPhoto.style.transform = 'scale(1)';
            }, 100);
            
            currentPhotoIndex = index;
        }
        
        function selectPhoto(index) {
            updatePhoto(index);
            playSound('click');
        }
        
        function nextPhoto() {
            const nextIndex = (currentPhotoIndex + 1) % photoMemories.length;
            updatePhoto(nextIndex);
            playSound('click');
        }
        
        function previousPhoto() {
            const prevIndex = (currentPhotoIndex - 1 + photoMemories.length) % photoMemories.length;
            updatePhoto(prevIndex);
            playSound('click');
        }
        
        function completePhotobooth() {
            alert('Beautiful! You\'ve seen all our precious memories! 📸💕');
            currentLevel = 5;
            showLevel('final');
            updateLoveMeter();
            playSound('success');
            createCelebration();
        }
        
        function answerQuiz(questionNum, answer) {
            const responses = {
                1: {
                    'heat': 'Yes! Best human heater ever! 🔥💕',
                    'cuddle': 'Cuddling with you is paradise! 🤗💖',
                    'baby': 'You treating me like a baby is the cutest! 👶🥰'
                },
                2: {
                    'kiss': 'That sneaky kiss was legendary! 💏✨',
                    'chiya': 'Chiya ghar dates are always special! ☕💕',
                    'mom': 'Haha your mom almost caught us! 😅🤫'
                },
                3: {
                    'bhoot': 'You were so scared in bhoot ghar! 👻😂',
                    'columbus': 'Columbus ride screaming champion! 🎢🏆',
                    'both': 'My brave scaredy cat girlfriend! 😂💕'
                }
            };
            
            alert(responses[questionNum][answer]);
            
            // Hide current question and show next
            document.getElementById(`quiz${questionNum}`).classList.add('hidden');
            quizProgress++;
            document.getElementById('quizScore').textContent = quizProgress;
            
            if (questionNum < 3) {
                document.getElementById(`quiz${questionNum + 1}`).classList.remove('hidden');
            } else {
                // Quiz completed
                setTimeout(() => {
                    alert('Perfect! You know our love story so well! 💕✨');
                    currentLevel = 4;
                    showLevel('level4');
                    updateLoveMeter();
                    playSound('success');
                }, 500);
            }
        }

        function openMemory() {
            document.getElementById('memoryText').classList.remove('hidden');
            document.getElementById('memoryBtn').classList.remove('hidden');
            playSound('click');
        }

        function saveMemory() {
            const memory = document.getElementById('memoryText').value;
            if (memory.trim()) {
                alert('Beautiful memory saved in my heart forever! 💕');
                currentLevel = 5;
                showLevel('final');
                updateLoveMeter();
                playSound('success');
                createCelebration();
            } else {
                alert('Please share a memory with me! 💭');
            }
        }

        function createCelebration() {
            // Create extra celebration effects
            for (let i = 0; i < 30; i++) {
                setTimeout(() => {
                    createHeart();
                    if (i % 3 === 0) createMusicNote();
                }, i * 100);
            }
        }

        function createFireworks() {
            const fireworks = ['🎆', '🎇', '✨', '🎊', '🌟'];
            for (let i = 0; i < 20; i++) {
                setTimeout(() => {
                    const firework = document.createElement('div');
                    firework.innerHTML = fireworks[Math.random() * fireworks.length | 0];
                    firework.style.position = 'absolute';
                    firework.style.left = Math.random() * window.innerWidth + 'px';
                    firework.style.top = Math.random() * window.innerHeight + 'px';
                    firework.style.fontSize = '40px';
                    firework.style.animation = 'sparkle 2s ease-out forwards';
                    firework.style.pointerEvents = 'none';
                    firework.style.zIndex = '10000';
                    document.body.appendChild(firework);
                    
                    setTimeout(() => {
                        if (firework.parentNode) {
                            firework.parentNode.removeChild(firework);
                        }
                    }, 2000);
                }, i * 200);
            }
            playSound('celebration');
        }

        function playAgain() {
            currentLevel = 0;
            danceScore = 0;
            quizProgress = 0;
            gameActive = false;
            document.getElementById('danceScore').textContent = '0';
            document.getElementById('quizScore').textContent = '0';
            document.getElementById('memoryText').value = '';
            document.getElementById('memoryText').classList.add('hidden');
            document.getElementById('memoryBtn').classList.add('hidden');
            document.getElementById('danceGameBtn').style.display = 'block';
            
            // Reset quiz questions
            document.getElementById('quiz1').classList.remove('hidden');
            document.getElementById('quiz2').classList.add('hidden');
            document.getElementById('quiz3').classList.add('hidden');
            
            showLevel('welcome');
            updateLoveMeter();
        }

        function showSurprise() {
            const personalSurprises = [
                "🚌 Remember that bus ride? You were my destiny! Best coincidence ever! 🚌✨",
                "🍦 Your love for blueberry ice cream = My love for you = INFINITE! 🍦💙",
                "👻 Bhoot ghar - you were so scared but I felt like your hero! 👻💪",
                "🎢 Columbus ride = You screaming + Me laughing = Perfect memory! 🎢😂",
                "🧋 Bubble tea dates and your sweet laughter - best combination! 🧋😍",
                "💏 That movie kiss when lights went off - almost caught by mom! 💏🤫",
                "📺 Netflix & chill with you = Paradise on earth! 📺🔥",
                "🛏️ Sleeping together sharing body heat = Best human heater! 🛏️💕",
                "👶 You treating me like baby = Most adorable girlfriend ever! 👶🥰",
                "☕ Chiya ghar dates and every conversation with you! ☕💕"
            ];
            
            const randomSurprise = personalSurprises[Math.floor(Math.random() * personalSurprises.length)];
            alert(randomSurprise);
            
            // Create themed celebration
            createCelebration();
            createFireworks();
        }

        // Simple sound effects using Web Audio API
        function playSound(type) {
            try {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();
                
                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);
                
                const frequencies = {
                    'click': 800,
                    'success': 1000,
                    'start': 600,
                    'celebration': 1200
                };
                
                oscillator.frequency.setValueAtTime(frequencies[type] || 800, audioContext.currentTime);
                oscillator.type = 'sine';
                
                gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);
                
                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + 0.3);
            } catch (e) {
                // Fallback for browsers that don't support Web Audio API
                console.log('Sound effect:', type);
            }
        }

        // Add keyboard shortcuts for fun
        document.addEventListener('keydown', (e) => {
            if (e.key === 'l' || e.key === 'L') {
                createHeart();
            }
            if (e.key === 'm' || e.key === 'M') {
                createMusicNote();
            }
            if (e.key === 'c' || e.key === 'C') {
                createFireworks();
            }
        });

        // Start with welcome screen and initial setup
        showLevel('welcome');
        updateLoveMeter();
        
        // Create initial atmosphere
        setTimeout(() => {
            for (let i = 0; i < 5; i++) {
                setTimeout(createHeart, i * 1000);
            }
        }, 1000);
    </script>
</body>
</html>
