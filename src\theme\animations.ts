export const animations = {
  // Durations
  durations: {
    fast: '150ms',
    normal: '300ms',
    slow: '500ms',
    slower: '750ms',
    slowest: '1000ms',
  },
  
  // Easing functions
  easings: {
    linear: 'linear',
    easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
    easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
    easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
    bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
    romantic: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)',
  },
  
  // Keyframes for common animations
  keyframes: {
    fadeIn: {
      from: { opacity: 0 },
      to: { opacity: 1 },
    },
    fadeOut: {
      from: { opacity: 1 },
      to: { opacity: 0 },
    },
    slideUp: {
      from: { transform: 'translateY(100%)', opacity: 0 },
      to: { transform: 'translateY(0)', opacity: 1 },
    },
    slideDown: {
      from: { transform: 'translateY(-100%)', opacity: 0 },
      to: { transform: 'translateY(0)', opacity: 1 },
    },
    scaleIn: {
      from: { transform: 'scale(0)', opacity: 0 },
      to: { transform: 'scale(1)', opacity: 1 },
    },
    pulse: {
      '0%, 100%': { transform: 'scale(1)' },
      '50%': { transform: 'scale(1.05)' },
    },
    float: {
      '0%, 100%': { transform: 'translateY(0px)' },
      '50%': { transform: 'translateY(-20px)' },
    },
    heartFloat: {
      '0%': {
        transform: 'translateY(100vh) rotate(0deg) scale(0.5)',
        opacity: 0,
      },
      '10%': {
        opacity: 1,
        transform: 'translateY(90vh) rotate(45deg) scale(1)',
      },
      '90%': {
        opacity: 1,
        transform: 'translateY(-50px) rotate(315deg) scale(1)',
      },
      '100%': {
        transform: 'translateY(-100px) rotate(360deg) scale(0.5)',
        opacity: 0,
      },
    },
    gradientShift: {
      '0%': { backgroundPosition: '0% 50%' },
      '25%': { backgroundPosition: '50% 0%' },
      '50%': { backgroundPosition: '100% 50%' },
      '75%': { backgroundPosition: '50% 100%' },
      '100%': { backgroundPosition: '0% 50%' },
    },
    sparkle: {
      '0%, 100%': { opacity: 1, transform: 'scale(1) rotate(0deg)' },
      '25%': { opacity: 0.7, transform: 'scale(1.3) rotate(90deg)' },
      '50%': { opacity: 0.4, transform: 'scale(1.5) rotate(180deg)' },
      '75%': { opacity: 0.7, transform: 'scale(1.3) rotate(270deg)' },
    },
  },
} as const

export type AnimationDuration = keyof typeof animations.durations
export type AnimationEasing = keyof typeof animations.easings
export type AnimationKeyframe = keyof typeof animations.keyframes
