import React from 'react'
import styled from '@emotion/styled'
import { <PERSON><PERSON>, Card, Typography } from '@/components/ui'

interface ErrorBoundaryState {
  hasError: boolean
  error?: Error
}

const ErrorContainer = styled.div`
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: ${({ theme }) => theme.spacing[4]};
  background: ${({ theme }) => theme.colors.gradients.dreamy};
`

const ErrorCard = styled(Card)`
  max-width: 500px;
  text-align: center;
`

const ErrorEmoji = styled.div`
  font-size: 4rem;
  margin-bottom: ${({ theme }) => theme.spacing[4]};
  animation: bounce 2s infinite;

  @keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
      transform: translateY(0);
    }
    40% {
      transform: translateY(-10px);
    }
    60% {
      transform: translateY(-5px);
    }
  }
`

const ButtonGroup = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing[3]};
  justify-content: center;
  margin-top: ${({ theme }) => theme.spacing[6]};

  ${({ theme }) => theme.mediaQueries.mobile} {
    flex-direction: column;
  }
`

export class ErrorBoundary extends React.Component<
  { children: React.ReactNode },
  ErrorBoundaryState
> {
  constructor(props: { children: React.ReactNode }) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Error caught by boundary:', error, errorInfo)
  }

  handleReload = () => {
    window.location.reload()
  }

  handleGoHome = () => {
    window.location.href = '/'
  }

  render() {
    if (this.state.hasError) {
      return (
        <ErrorContainer>
          <ErrorCard variant="glass" padding="xl">
            <ErrorEmoji>💔</ErrorEmoji>
            
            <Typography variant="h2" gradient>
              Oops! Something went wrong
            </Typography>
            
            <Typography variant="body" align="center" style={{ marginTop: '1rem' }}>
              Don't worry, our love story is still beautiful! 💕<br />
              Let's try to get back on track.
            </Typography>
            
            {process.env.NODE_ENV === 'development' && this.state.error && (
              <details style={{ marginTop: '2rem', textAlign: 'left' }}>
                <summary style={{ cursor: 'pointer', marginBottom: '1rem' }}>
                  <Typography variant="caption">
                    Error Details (Development)
                  </Typography>
                </summary>
                <pre style={{ 
                  background: '#f5f5f5', 
                  padding: '1rem', 
                  borderRadius: '8px',
                  fontSize: '0.8rem',
                  overflow: 'auto',
                  maxHeight: '200px'
                }}>
                  {this.state.error.stack}
                </pre>
              </details>
            )}
            
            <ButtonGroup>
              <Button variant="romantic" onClick={this.handleReload}>
                Try Again 🔄
              </Button>
              <Button variant="secondary" onClick={this.handleGoHome}>
                Go Home 🏠
              </Button>
            </ButtonGroup>
          </ErrorCard>
        </ErrorContainer>
      )
    }

    return this.props.children
  }
}

export default ErrorBoundary
